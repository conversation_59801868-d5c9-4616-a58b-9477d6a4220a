{"common.home": "Trang chủ", "system": "<PERSON><PERSON><PERSON><PERSON> tạo hình <PERSON>nh <PERSON>", "helloWorld": "Xin chào thế giới!", "Describe the image you want to generate...": "<PERSON><PERSON> tả hình <PERSON>nh bạn muốn tạo...", "Describe the video you want to generate...": "<PERSON><PERSON> tả video bạn muốn tạo...", "appTitle": "<PERSON><PERSON><PERSON> hình <PERSON>nh bằng AI", "copyright": "<PERSON><PERSON><PERSON> quy<PERSON> © {year}, GeminiGen.AI", "available": "<PERSON><PERSON> sẵn cho các dự án mới", "notAvailable": "<PERSON><PERSON><PERSON> không có sẵn", "blog": "Blog", "copyLink": "<PERSON><PERSON> ch<PERSON><PERSON> liên kết", "minRead": "PHÚT ĐỌC", "articleLinkCopied": "<PERSON><PERSON><PERSON> kết bài viết đã đư<PERSON>c sao chép vào khay nhớ tạm", "clickToClose": "<PERSON><PERSON><PERSON><PERSON> vào bất kỳ đâu hoặc nhấn ESC để đóng lại", "promptDetails": "<PERSON> tiết gợi ý", "generateWithPrompt": "Tạo ảnh với gợi ý này", "generateWithSettings": "Generate với bộ cài đặt này", "preset": "Cài đặt sẵn", "style": "<PERSON><PERSON> c<PERSON>ch", "resolution": "<PERSON><PERSON> phân giải", "addImage": "<PERSON><PERSON><PERSON><PERSON>", "modelPreset": "<PERSON>ô hình/Cài đặt sẵn", "imageDimensions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourImage": "<PERSON><PERSON><PERSON> c<PERSON>a bạn", "generate": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "personGeneration.dontAllow": "<PERSON><PERSON><PERSON><PERSON> cho phép", "personGeneration.allowAdult": "<PERSON> phép có người lớn", "personGeneration.allowAll": "<PERSON> phép tất cả", "safetyFilter.blockLowAndAbove": "Chặn thấp trở lên", "safetyFilter.blockMediumAndAbove": "Chặn trung bình trở lên", "safetyFilter.blockOnlyHigh": "Chỉ chặn cao", "safetyFilter.blockNone": "Không chặn", "nav.aitool": "C<PERSON>ng cụ AI", "nav.history": "<PERSON><PERSON><PERSON> s<PERSON>", "nav.orders": "<PERSON><PERSON><PERSON> hàng", "nav.api": "API", "nav.login": "<PERSON><PERSON><PERSON>", "3D Render": "Kết xu<PERSON>", "Acrylic": "Sơn acrylic", "Anime General": "<PERSON><PERSON> chung", "Creative": "<PERSON><PERSON><PERSON>", "Dynamic": "<PERSON><PERSON><PERSON> động", "Fashion": "<PERSON><PERSON><PERSON><PERSON> trang", "Game Concept": "Concept game", "Graphic Design 3D": "<PERSON><PERSON><PERSON><PERSON> kế đồ họa 3D", "Illustration": "<PERSON>", "None": "K<PERSON>ô<PERSON>", "Portrait": "Chân dung", "Portrait Cinematic": "<PERSON><PERSON> dung điện <PERSON>nh", "Portrait Fashion": "<PERSON>ân dung thời trang", "Ray Traced": "<PERSON><PERSON><PERSON> xu<PERSON>t theo ray tracing", "Stock Photo": "Ảnh stock", "Watercolor": "<PERSON><PERSON><PERSON>", "voice": "<PERSON><PERSON><PERSON><PERSON> nói", "emotion": "<PERSON><PERSON><PERSON>", "speed": "<PERSON><PERSON><PERSON>", "speed_settings": "<PERSON>ài đặt tốc độ", "speed_value": "<PERSON><PERSON><PERSON> trị tốc độ", "speed_slider": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> tốc độ", "apply": "<PERSON><PERSON>", "speech_settings": "Cài đặt giọng nói", "current_speed": "<PERSON><PERSON><PERSON> độ hiện tại", "reset_defaults": "Đặt lại mặc định", "outputFormat": "<PERSON><PERSON><PERSON> dạng xuất", "outputChannel": "<PERSON><PERSON><PERSON>", "selectVoice": "<PERSON><PERSON><PERSON> g<PERSON> nói", "selectEmotion": "<PERSON><PERSON><PERSON> c<PERSON> x<PERSON>c", "selectFormat": "<PERSON><PERSON><PERSON> đ<PERSON>nh dạng", "selectChannel": "<PERSON><PERSON><PERSON>", "noVoicesAvailable": "K<PERSON>ông có giọng nói nào", "noEmotionsAvailable": "<PERSON><PERSON><PERSON><PERSON> có cảm xúc nào", "searchVoices": "<PERSON><PERSON>m kiếm giọng nói...", "searchEmotions": "<PERSON><PERSON><PERSON> kiếm cảm xúc...", "noVoicesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giọng nói", "noEmotionsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cảm xúc", "retry": "<PERSON><PERSON><PERSON> lại", "noAudioSample": "<PERSON><PERSON><PERSON><PERSON> có mẫu âm thanh", "Speech Generation Complete": "<PERSON><PERSON><PERSON> gi<PERSON>ng nói hoàn thành", "Your speech has been generated successfully": "<PERSON><PERSON><PERSON><PERSON> nói của bạn đã đư<PERSON><PERSON> tạo thành công", "Describe the speech you want to generate...": "<PERSON><PERSON> tả giọng nói bạn muốn tạo...", "AI Image Generator": "<PERSON><PERSON><PERSON><PERSON> tạo hình <PERSON>nh <PERSON>", "Generate AI images from text prompts with a magical particle transformation effect": "<PERSON><PERSON><PERSON>nh <PERSON>nh AI từ gợi ý văn bản với hiệu ứng biến đổi hạt ma thuật", "Enter your prompt": "<PERSON><PERSON><PERSON><PERSON> gợi ý của bạn", "Generating...": "<PERSON><PERSON> tạo...", "Generate Image": "<PERSON><PERSON><PERSON>", "Enter a prompt and click Generate Image to create an AI image": "<PERSON><PERSON><PERSON>p gợi ý và nhấp vào [T<PERSON><PERSON> hình <PERSON>nh] để tạo hình ảnh AI", "Prompt:": "Gợi ý:", "Download": "<PERSON><PERSON><PERSON>", "How It Works": "<PERSON><PERSON><PERSON> ho<PERSON> động", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Trình tạo hình ảnh AI này sử dụng hiệu ứng biến đổi dựa trên hạt để hiển thị quá trình tạo. <PERSON>hi bạn nhập gợi ý và nhấp vào 'Tạo', hệ thống sẽ:", "Sends your prompt to an AI image generation API": "<PERSON><PERSON><PERSON> gợi ý của bạn đến API tạo hình ảnh AI", "Creates a particle system with thousands of tiny particles": "<PERSON><PERSON><PERSON> hệ thống hạt với hàng nghìn hạt nhỏ", "Transforms the random noise particles into the generated image": "<PERSON>iến đổi các hạt nhiễu ngẫu nhiên thành hình ảnh được tạo", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "<PERSON><PERSON><PERSON> hạt bắt đầu ở dạng nhiễu ngẫu nhiên và sau đó biến đổi mượt mà thành hình ảnh cuối cùng, tạo ra hiệu ứng ma thuật mô phỏng quá trình sáng tạo của AI.", "Transform": "<PERSON><PERSON><PERSON><PERSON> đổi", "Transforming...": "<PERSON><PERSON> biến đổi...", "Initializing particles...": "<PERSON><PERSON> khởi tạo các hạt...", "Loading image...": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "Creating particle system...": "<PERSON><PERSON> tạo hệ thống hạt...", "Adding event listeners...": "<PERSON><PERSON> thêm trình lắng nghe sự kiện...", "Ready!": "Sẵn sàng!", "AI Image Particle Effect": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>t h<PERSON>nh <PERSON>", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "<PERSON> họa về thành phần BaseMagicImage biến đổi các hạt thành hình ảnh được tạo bởi AI", "Click anywhere or press ESC to close": "<PERSON><PERSON><PERSON><PERSON> vào bất kỳ đâu hoặc nhấn ESC để đóng lại", "auth.login": "<PERSON><PERSON><PERSON>", "auth.loginDescription": "<PERSON><PERSON><PERSON> nhập vào tài kho<PERSON>n của bạn để tiếp tục", "auth.email": "Email", "auth.enterEmail": "Nhập email c<PERSON><PERSON> bạn", "auth.filter": "<PERSON><PERSON> lọc", "auth.password": "<PERSON><PERSON><PERSON>", "auth.enterPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn", "auth.rememberMe": "<PERSON><PERSON> nhớ đăng nhập", "auth.welcomeBack": "<PERSON><PERSON><PERSON> mừng trở lại", "auth.dontHaveAccount": "Chưa có tài k<PERSON>n?", "auth.signUp": "<PERSON><PERSON><PERSON> ký", "auth.forgotPassword": "<PERSON>uên mật khẩu?", "auth.signupFailed": "<PERSON><PERSON><PERSON> ký thất bại", "auth.signupFailedDescription": "<PERSON><PERSON> xảy ra lỗi trong quá trình đăng ký. <PERSON><PERSON> lòng thử lại.", "auth.bySigningIn": "Bằng cách đăng <PERSON>, bạn đồng ý với", "auth.termsOfService": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ", "auth.signUpTitle": "<PERSON><PERSON><PERSON> ký", "auth.signUpDescription": "<PERSON><PERSON><PERSON> tài kho<PERSON>n để bắt đầu", "auth.name": "<PERSON><PERSON><PERSON>", "auth.enterName": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "auth.createAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "auth.alreadyHaveAccount": "Đã có tài k<PERSON>n?", "auth.bySigningUp": "Bằng cách đăng ký, bạn đồng ý với", "auth.backToHome": "Quay lại trang chủ", "auth.notVerifyAccount": "<PERSON><PERSON><PERSON> khoản của bạn chưa được xác minh. <PERSON><PERSON> lòng xác minh tài khoản để tiếp tục", "auth.verifyAccount": "<PERSON><PERSON><PERSON>h tài <PERSON>n", "auth.resendActivationEmail": "Gửi l<PERSON>i email k<PERSON>ch ho<PERSON>t", "auth.accountRecovery": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> tài <PERSON>n", "auth.accountRecoveryTitle": "<PERSON><PERSON><PERSON><PERSON> phục tài k<PERSON>n của bạn", "auth.accountRecoveryDescription": "Nhập email của bạn để nhận hướng dẫn đặt lại mật khẩu", "auth.sendRecoveryEmail": "Gửi email khôi phục", "auth.recoveryEmailSent": "<PERSON><PERSON> kh<PERSON>i phục đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "auth.recoveryEmailSentDescription": "<PERSON><PERSON> lòng kiểm tra email của bạn để biết hướng dẫn đặt lại mật khẩu", "auth.resetPassword": "Đặt lại mật khẩu", "auth.resetPasswordTitle": "Đặt lại mật khẩu của bạn", "auth.resetPasswordDescription": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mới của bạn", "auth.newPassword": "<PERSON><PERSON><PERSON> mới", "auth.confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "auth.enterNewPassword": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mới của bạn", "auth.enterConfirmPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới của bạn", "auth.passwordResetSuccess": "Đặt lại mật khẩu thành công", "auth.passwordResetSuccessDescription": "Mật khẩu của bạn đã được đặt lại thành công. Bây giờ bạn có thể đăng nhập bằng mật khẩu mới", "auth.activateAccount": "<PERSON><PERSON><PERSON> ho<PERSON> tà<PERSON>", "auth.activateAccountTitle": "<PERSON><PERSON><PERSON> ho<PERSON>t tài k<PERSON>n của bạn", "auth.activateAccountDescription": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đang đư<PERSON><PERSON> k<PERSON>ch ho<PERSON>...", "auth.accountActivated": "<PERSON><PERSON><PERSON> k<PERSON>n đã đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>t", "auth.accountActivatedDescription": "<PERSON><PERSON><PERSON> khoản của bạn đã đư<PERSON><PERSON> kích hoạt thành công. B<PERSON>y giờ bạn có thể đăng nhập", "auth.activationFailed": "<PERSON><PERSON><PERSON> ho<PERSON>t thất bại", "auth.activationFailedDescription": "<PERSON><PERSON><PERSON><PERSON> thể kích hoạt tài khoản của bạn. <PERSON><PERSON> lòng thử lại hoặc liên hệ hỗ trợ", "auth.backToLogin": "Quay lại đăng nh<PERSON>p", "auth.loginFailed": "<PERSON><PERSON><PERSON> nh<PERSON>p thất bại", "auth.loginWithGoogle": "<PERSON><PERSON><PERSON> nhập bằng Google", "auth.google": "Google", "validation.invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "validation.passwordMinLength": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "validation.nameRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "validation.required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "validation.passwordsDoNotMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "imageSelect.pleaseSelectImageFile": "<PERSON><PERSON> lòng chọn một tệp hình <PERSON>nh", "imageSelect.selectedImage": "<PERSON><PERSON><PERSON>nh đã chọn", "imageSelect.removeImage": "<PERSON><PERSON><PERSON>", "pixelReveal.loading": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "pixelReveal.processing": "<PERSON><PERSON> x<PERSON> lý hình <PERSON>nh...", "pixelReveal.revealComplete": "<PERSON><PERSON><PERSON> thị hình <PERSON>nh hoàn tất", "SIGNIN_WRONG_EMAIL_PASSWORD": "Email hoặc mật khẩu sai", "Try again": "<PERSON><PERSON><PERSON> lại", "aiToolMenu.imagen": "Tạo Ảnh", "aiToolMenu.videoGen": "Tạo Video", "aiToolMenu.speechGen": "Tạo Giọ<PERSON>ó<PERSON>", "aiToolMenu.musicGen": "Tạo <PERSON>", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "<PERSON><PERSON><PERSON> hình <PERSON>nh chất lư<PERSON> cao, chi tiết với khả năng hiển thị văn bản chính xác cho nội dung thị giác sáng tạo.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "<PERSON>h<PERSON> hiện ý tưởng của bạn như chưa từng có — v<PERSON><PERSON>, sự sáng tạo không có giới hạn.", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash là công cụ mạnh mẽ để tạo hình ảnh từ câu lệnh văn bản.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> nh<PERSON>t quán và sự sáng tạo tốt hơn bao giờ hết.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "Video gặp gỡ âm thanh. <PERSON><PERSON> hình tạo video mới nhất của chúng tô<PERSON>, đ<PERSON><PERSON><PERSON> thiết kế để trao quyền cho các nhà làm phim và người kể chuyện.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "<PERSON><PERSON> hình text-to-speech tiên tiến nhất hiện có.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "<PERSON><PERSON> lý quy mô lớn (ví dụ: nhiều tệp PDF).\n<PERSON><PERSON><PERSON> tác vụ khối lượ<PERSON> lớn, độ trễ thấp đòi hỏi suy nghĩ\n<PERSON>c trường hợp sử dụng Agentic", "aiToolMenu.link": "<PERSON><PERSON><PERSON>", "aiToolMenu.linkDescription": "Sử dụng NuxtLink với siêu năng lực.", "aiToolMenu.soon": "Sớm", "readArticle": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> vi<PERSON>", "switchToLightMode": "<PERSON><PERSON><PERSON><PERSON> sang chế độ sáng", "switchToDarkMode": "<PERSON><PERSON><PERSON><PERSON> sang chế độ tối", "profile": "<PERSON><PERSON> sơ", "buyCredits.checkout": "Đặt hàng", "buyCredits.checkoutDescription": "<PERSON><PERSON><PERSON> nhận đơn hàng của bạn sau đó chọn phư<PERSON><PERSON> thức thanh toán.", "buyCredits.orderDetail": "<PERSON> tiết đơn hàng", "buyCredits.credits": "Credit", "buyCredits.pricePerUnit": "Đơn giá", "buyCredits.totalCredits": "Tổng credit", "buyCredits.totalPrice": "<PERSON><PERSON><PERSON> tiền", "buyCredits.payment": "<PERSON><PERSON> toán", "buyCredits.submit": "<PERSON><PERSON><PERSON> t<PERSON>t", "buyCredits.cancel": "<PERSON><PERSON><PERSON>", "pricing.title": "Bảng giá", "pricing.description": "<PERSON><PERSON><PERSON> gói hoàn hảo cho nhu cầu tạo hình <PERSON>nh của bạn", "pricing.comingSoon": "S<PERSON><PERSON> ra mắt", "pricing.comingSoonDescription": "<PERSON><PERSON><PERSON> gói giá của chúng tôi đang đượ<PERSON> hoàn thiện. H<PERSON>y quay lại sớm để cập nhật.", "magicImageDemo.title": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>t h<PERSON>nh <PERSON>", "magicImageDemo.description": "<PERSON> họa về thành phần BaseMagicImage biến đổi các hạt thành hình ảnh được tạo bởi AI", "magicImageDemo.image": "<PERSON><PERSON><PERSON>", "magicImageDemo.aboutTitle": "<PERSON><PERSON>", "magicImageDemo.aboutDescription": "Th<PERSON><PERSON> phần BaseMagicImage sử dụng Three.js để tạo hệ thống hạt có thể biến đổi giữa các vị trí ngẫu nhiên và hình ảnh được tạo bởi AI. <PERSON><PERSON><PERSON> hạt di chuyển với hiệu ứng xoay và chảy, tạo ra sự biến đổi ma thuật.", "magicImageDemo.featuresTitle": "<PERSON><PERSON><PERSON>", "magicImageDemo.features.particleRendering": "<PERSON><PERSON><PERSON> xu<PERSON>t hình <PERSON>nh dựa trên hạt", "magicImageDemo.features.smoothTransitions": "Chuyển đổi mượt mà giữa vị trí hạt ngẫu nhiên và hình thành hình ảnh", "magicImageDemo.features.interactiveControls": "<PERSON><PERSON><PERSON><PERSON> khiển camera tư<PERSON><PERSON> tác (kéo để xoay, cuộn để phóng to)", "magicImageDemo.features.customizable": "Số lượng hạt và thời gian hoạt hình có thể tùy chỉnh", "magicImageDemo.features.automatic": "<PERSON><PERSON><PERSON> hoạt biến đổi tự động hoặc thủ công", "magicImageDemo.howItWorksTitle": "<PERSON><PERSON><PERSON>", "magicImageDemo.howItWorksDescription": "Thành phần phân tích các pixel của hình ảnh và tạo hệ thống hạt 3D trong đó mỗi hạt đại diện cho một pixel. Các pixel sáng hơn được định vị gần người xem hơn, tạo hiệu ứng 3D tinh tế. <PERSON><PERSON>c hạt ban đầu được phân tán ngẫu nhiên trong không gian 3D, sau đó hoạt hình để tạo thành hình ảnh khi được kích hoạt.", "privacy.title": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật", "privacy.description": "<PERSON><PERSON><PERSON> hi<PERSON>u cách chúng tôi bảo vệ quyền riêng tư và xử lý dữ liệu của bạn", "privacy.informationWeCollect": "Thông tin chúng tôi thu thập", "privacy.informationWeCollectDescription": "<PERSON>úng tôi thu thập thông tin bạn cung cấp trực tiếp cho chúng tôi, chẳng hạn như khi bạn tạo tà<PERSON>, sử dụng dịch vụ của chúng tôi hoặc liên hệ với chúng tôi để được hỗ trợ.", "privacy.howWeUseInformation": "<PERSON><PERSON><PERSON> chúng tôi sử dụng thông tin của bạn", "privacy.howWeUseInformationDescription": "<PERSON>úng tôi sử dụng thông tin thu thập đ<PERSON><PERSON><PERSON> để cung cấp, duy trì và cải thiện dịch vụ của mình, x<PERSON> lý giao dịch và liên lạc với bạn.", "privacy.informationSharing": "Chia sẻ thông tin", "privacy.informationSharingDescription": "<PERSON><PERSON>g tôi không bán, trao đổi hoặc chuyển giao thông tin cá nhân của bạn cho bên thứ ba mà không có sự đồng ý của bạn, trừ những trường hợp được mô tả trong chính sách này.", "privacy.dataSecurity": "<PERSON><PERSON><PERSON> mật dữ liệu", "privacy.dataSecurityDescription": "<PERSON><PERSON>g tôi thực hiện các biện pháp bảo mật thích hợp để bảo vệ thông tin cá nhân của bạn khỏi truy cập, thay đổi, tiế<PERSON> lộ hoặc phá hủy trái phép.", "privacy.contactUs": "<PERSON><PERSON><PERSON> h<PERSON> với chúng tôi", "privacy.contactUsDescription": "<PERSON><PERSON>u bạn có bất kỳ câu hỏi nào về Chính sách Bảo mật này, vui lòng liên hệ với chúng tôi qua các kênh hỗ trợ của chúng tôi.", "terms.title": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ", "terms.description": "<PERSON><PERSON><PERSON> điều kho<PERSON>n và điều kiện sử dụng dịch v<PERSON>n", "terms.acceptanceOfTerms": "1. <PERSON><PERSON><PERSON> đi<PERSON><PERSON>", "terms.acceptanceOfTermsDescription": "Bằng cách truy cập và sử dụng dịch v<PERSON>, bạn chấp nhận và đồng ý bị ràng buộc bởi các điều khoản và điều kiện của thỏa thuận này.", "terms.useOfService": "2. <PERSON><PERSON> d<PERSON><PERSON> dịch vụ", "terms.useOfServiceDescription": "Bạn đồng ý chỉ sử dụng dịch vụ của chúng tôi cho các mục đích hợp pháp và theo các <PERSON> khoản Dịch vụ này.", "terms.userAccounts": "3. <PERSON><PERSON><PERSON> ng<PERSON><PERSON>i dùng", "terms.userAccountsDescription": "Bạn có trách nhiệm duy trì tính bảo mật của tài khoản và mật khẩu của mình.", "terms.intellectualProperty": "4. Sở hữu trí tuệ", "terms.intellectualPropertyDescription": "Tất cả nội dung và tài liệu có sẵn trên dịch vụ của chúng tôi đều được bảo vệ bởi quyền sở hữu trí tuệ.", "terms.termination": "5. <PERSON><PERSON><PERSON>", "terms.terminationDescription": "<PERSON>úng tôi có thể chấm dứt hoặc tạm ngừng tài khoản và quyền truy cập dịch vụ của bạn theo quyết định riêng của chúng tôi.", "terms.disclaimers": "6. <PERSON><PERSON><PERSON><PERSON> b<PERSON> từ chối", "terms.disclaimersDescription": "<PERSON><PERSON><PERSON> v<PERSON> đư<PERSON><PERSON> cung cấ<PERSON> '<PERSON><PERSON><PERSON> hiện tại' mà không có bất kỳ bảo đảm nào.", "terms.contactUsTerms": "<PERSON><PERSON><PERSON> h<PERSON> với chúng tôi", "terms.contactUsTermsDescription": "<PERSON><PERSON>u bạn có bất kỳ câu hỏi nào về các Điều khoản Dịch vụ này, vui lòng liên hệ với chúng tôi qua các kênh hỗ trợ của chúng tôi.", "appName": "GeminiGen.AI", "quickTopUp": "<PERSON><PERSON><PERSON>", "customTopUp": "<PERSON><PERSON><PERSON> tùy chỉnh", "numberOfCredits": "Số lượng credit", "paypal": "PayPal", "paypalDescription": "<PERSON><PERSON> toán an toàn bằng tài khoản PayPal của bạn", "stripe": "Stripe", "stripeDescription": "<PERSON>h toán an toàn bằng Stripe", "debitCreditCard": "Thẻ ghi nợ hoặc thẻ tín dụng", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Thanh toán bằng Crypto", "cryptoDescription": "Bitcoin, Ethereum và các loại tiền điện tử khác", "profileMenu.guide": "Hướng dẫn", "profileMenu.logo": "Logo", "profileMenu.settings": "Cài đặt", "profileMenu.components": "<PERSON><PERSON><PERSON><PERSON> phần", "loadingMoreItems": "<PERSON><PERSON> tải thêm mục...", "history.tabs.imagen": "Imagen", "history.tabs.video": "Video", "history.tabs.speech": "<PERSON><PERSON><PERSON><PERSON> nói", "history.tabs.music": "Nhạc", "history.tabs.history": "<PERSON><PERSON><PERSON> s<PERSON>", "orders.title": "<PERSON><PERSON><PERSON> sử giao dịch", "orders.description": "<PERSON><PERSON> l<PERSON>ch sử giao dịch và thanh to<PERSON> của bạn", "orders.orderId": "<PERSON><PERSON> đơn hàng", "orders.amount": "<PERSON><PERSON> tiền", "orders.credits": "Credits", "orders.quantity": "Số lượng", "orders.platform": "<PERSON><PERSON><PERSON> t<PERSON>", "orders.externalId": "Mã giao d<PERSON>ch", "orders.status.completed": "<PERSON><PERSON><PERSON> th<PERSON>", "orders.status.success": "<PERSON><PERSON><PERSON><PERSON> công", "orders.status.paid": "<PERSON><PERSON> thanh toán", "orders.status.pending": "<PERSON><PERSON> lý", "orders.status.processing": "<PERSON><PERSON> lý", "orders.status.failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "orders.status.cancelled": "<PERSON><PERSON> hủy", "orders.status.error": "Lỗi", "orders.empty.title": "Chưa có giao dịch nào", "orders.empty.description": "Bạn chưa có giao dịch nào. <PERSON><PERSON><PERSON> mua credits để bắt đầu sử dụng dịch vụ.", "orders.empty.action": "<PERSON><PERSON>", "orders.endOfList": "Bạn đã xem hết tất cả giao dịch", "orders.errors.fetchFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải lịch sử giao dịch. <PERSON><PERSON> lòng thử lại.", "orders.meta.title": "<PERSON><PERSON><PERSON> sử giao d<PERSON> - Imagen AI", "orders.meta.description": "<PERSON><PERSON> l<PERSON>ch sử giao dịch và thanh toán của bạn trên <PERSON>", "promptLabel": "Gợi ý:", "videoExamples": "<PERSON><PERSON>", "videoExamplesDescription": "<PERSON>h<PERSON>m phá các ví dụ video với prompt và cài đặt của chúng. Nhấp vào nút 'Sử dụng Prompt này' để sao chép prompt vào ô nhập liệu của bạn.", "useThisPrompt": "Sử dụng Prompt này", "model": "<PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "videoTypeSelection": "<PERSON><PERSON><PERSON> video", "notifications.title": "<PERSON><PERSON><PERSON><PERSON> báo", "notifications.description": "<PERSON><PERSON><PERSON><PERSON> báo và cập nhật gần đây của bạn", "notifications.totalCount": "{count} thông báo", "notifications.markAllRead": "<PERSON><PERSON><PERSON> dấu tất cả đã đọc", "notifications.loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>", "notifications.close": "Đ<PERSON><PERSON>", "notifications.empty.title": "<PERSON><PERSON><PERSON>ng có thông báo", "notifications.empty.description": "Bạn đã cập nhật hết! Không có thông báo mới để hiển thị.", "notifications.error.title": "Lỗi khi tải thông báo", "notifications.time.justNow": "<PERSON><PERSON><PERSON> xong", "notifications.time.minutesAgo": "{minutes} ph<PERSON>t trước", "notifications.time.hoursAgo": "{hours} gi<PERSON> trước", "notifications.time.yesterday": "<PERSON><PERSON><PERSON> qua", "notifications.status.processing.title": "<PERSON><PERSON> lý", "notifications.status.processing.description": "<PERSON><PERSON><PERSON> cầu của bạn đang được xử lý", "notifications.status.success.title": "<PERSON><PERSON><PERSON> th<PERSON>", "notifications.status.success.description": "<PERSON><PERSON> hoàn thành thành công", "notifications.status.failed.title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "notifications.status.failed.description": "<PERSON><PERSON> xảy ra lỗi trong quá trình xử lý", "notifications.status.warning.title": "<PERSON><PERSON><PERSON> b<PERSON>o", "notifications.status.warning.description": "<PERSON><PERSON><PERSON> thành với cảnh báo", "notifications.status.pending.title": "<PERSON><PERSON> chờ", "notifications.status.pending.description": "<PERSON><PERSON> chờ đư<PERSON><PERSON> xử lý", "notifications.status.cancelled.title": "<PERSON><PERSON> hủy", "notifications.status.cancelled.description": "<PERSON><PERSON><PERSON> c<PERSON>u đã bị hủy", "notifications.types.video_1.title": "Tạo video đang chờ", "notifications.types.video_1.description": "Tạo video đang chờ được xử lý", "notifications.types.video_2.title": "Tạo video hoàn tất", "notifications.types.video_2.description": "Video đã đ<PERSON><PERSON><PERSON> tạo thành công", "notifications.types.video_3.title": "Tạo video thất bại", "notifications.types.video_3.description": "Tạo video đã thất bại", "notifications.types.image_1.title": "<PERSON><PERSON><PERSON> hình <PERSON>nh đang chờ", "notifications.types.image_1.description": "<PERSON><PERSON><PERSON> hình <PERSON>nh đang chờ được xử lý", "notifications.types.image_2.title": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh hoàn tất", "notifications.types.image_2.description": "<PERSON><PERSON><PERSON> ảnh đã đư<PERSON><PERSON> tạo thành công", "notifications.types.image_3.title": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh thất bại", "notifications.types.image_3.description": "<PERSON><PERSON><PERSON> hình <PERSON>nh đã thất bại", "notifications.types.tts_history_1.title": "<PERSON><PERSON><PERSON> âm thanh đang chờ", "notifications.types.tts_history_1.description": "Chuyển văn bản thành giọng nói đang chờ được xử lý", "notifications.types.tts_history_2.title": "<PERSON><PERSON><PERSON> âm thanh hoàn tất", "notifications.types.tts_history_2.description": "<PERSON><PERSON> thanh chuyển văn bản thành giọng nói đã được tạo thành công", "notifications.types.tts_history_3.title": "<PERSON><PERSON><PERSON> âm thanh thất bại", "notifications.types.tts_history_3.description": "Chuyển văn bản thành giọng nói đã thất bại", "notifications.types.voice_training_1.title": "Huấn luyện giọng nói đang chờ", "notifications.types.voice_training_1.description": "<PERSON>ấn luyện giọng nói đang chờ được xử lý", "notifications.types.voice_training_2.title": "<PERSON><PERSON><PERSON> luyện giọng nói hoàn tất", "notifications.types.voice_training_2.description": "Huấn luyện mô hình giọng nói tùy chỉnh đã hoàn thành thành công", "notifications.types.voice_training_3.title": "<PERSON><PERSON>n luyện giọng nói thất bại", "notifications.types.voice_training_3.description": "<PERSON><PERSON>n luyện giọng nói đã thất bại", "notifications.types.music_1.title": "<PERSON><PERSON><PERSON> nh<PERSON>c đang chờ", "notifications.types.music_1.description": "<PERSON><PERSON><PERSON> nh<PERSON>c đang chờ được xử lý", "notifications.types.music_2.title": "<PERSON><PERSON><PERSON> <PERSON>c hoàn tất", "notifications.types.music_2.description": "Nhạc AI đã đư<PERSON><PERSON> tạo thành công", "notifications.types.music_3.title": "<PERSON><PERSON><PERSON> n<PERSON>c thất bại", "notifications.types.music_3.description": "<PERSON><PERSON><PERSON> nhạc đã thất bại", "notifications.types.speech_1.title": "Tạo g<PERSON>ng nói đang chờ", "notifications.types.speech_1.description": "<PERSON><PERSON><PERSON> cầu tạo giọng nói đang chờ được xử lý", "notifications.types.speech_2.title": "<PERSON><PERSON><PERSON> gi<PERSON>ng nói hoàn thành", "notifications.types.speech_2.description": "<PERSON><PERSON><PERSON><PERSON> nói đã đư<PERSON><PERSON> tạo thành công", "notifications.types.speech_3.title": "<PERSON><PERSON>o g<PERSON>ng nói thất bại", "notifications.types.speech_3.description": "<PERSON><PERSON>o giọng nói đã thất bại. <PERSON><PERSON> lòng thử lại", "notifications.types.default.title": "<PERSON><PERSON><PERSON><PERSON> báo", "notifications.types.default.description": "Bạn có một thông báo mới", "footer.nuxtUIOnDiscord": "Nuxt U<PERSON> trên <PERSON>", "historyFilter.all": "<PERSON><PERSON><PERSON> c<PERSON>", "historyFilter.imagen": "Imagen", "historyFilter.videoGen": "Video Gen", "historyFilter.speechGen": "Speech Gen", "historyFilter.dialogueGen": "<PERSON><PERSON><PERSON>", "historyFilter.speechGenDocument": "<PERSON><PERSON><PERSON> từ Tà<PERSON>", "historyPages.backToLibrary": "<PERSON><PERSON> lại thư viện", "historyPages.aiContentLibraryTitle": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> nội dung <PERSON>", "historyPages.aiContentLibraryDescription": "<PERSON><PERSON><PERSON><PERSON> phá thư viện nội dung AI đư<PERSON><PERSON> tạo ra", "historyPages.imagenDescription": "Duyệt qua hình ảnh và tác phẩm nghệ thuật do AI tạo ra của bạn", "historyPages.musicDescription": "Duyệt qua nội dung nhạc và âm thanh do AI tạo ra của bạn", "historyPages.speechDescription": "Duyệt qua nội dung bài phát biểu và giọng nói do AI tạo ra của bạn", "historyPages.videoDescription": "Duyệt qua video và hoạt hình do AI tạo ra của bạn.", "historyPages.imagenBreadcrumb": "<PERSON><PERSON><PERSON>", "historyPages.musicBreadcrumb": "<PERSON><PERSON>", "historyPages.speechBreadcrumb": "<PERSON><PERSON><PERSON> ph<PERSON>t bi<PERSON>u", "historyPages.videoBreadcrumb": "Tạo video", "historyPages.endOfImagesHistory": "Bạn đã xem hết lịch sử hình <PERSON>nh.", "historyPages.endOfMusicHistory": "Bạn đã đi tới phần cuối của lịch sử âm nhạc.", "historyPages.endOfSpeechHistory": "Bạn đã đến phần cuối của lịch sử bài phát biểu.", "historyPages.endOfVideoHistory": "<PERSON><PERSON>n đã xem hết lịch sử video.", "historyPages.noVideosFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy video nào", "historyPages.noVideosFoundDescription": "<PERSON><PERSON><PERSON> đầu tạo video để xem chúng ở đây.", "historyPages.errorLoadingVideo": "Lỗi Tải Video", "historyPages.loadingVideoDetails": "<PERSON><PERSON> tải chi tiết video...", "historyPages.videoDetails": "<PERSON> ti<PERSON> video", "historyPages.videoInformation": "Thông tin video", "historyPages.videoNotFound": "Video bạn đang tìm không thể tìm thấy hoặc tải được.", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "Cài đặt", "userMenu.profile": "<PERSON><PERSON> sơ", "userMenu.buyCredits": "<PERSON><PERSON> tín dụng", "userMenu.settings": "Cài đặt", "userMenu.api": "API", "userMenu.logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "userMenu.greeting": "<PERSON><PERSON> ch<PERSON>, {name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "Stereo", "options.allow": "<PERSON> phép", "options.dontAllow": "<PERSON><PERSON><PERSON><PERSON> cho phép", "options.voices": "<PERSON><PERSON><PERSON><PERSON> nói", "options.pickVoice": "<PERSON><PERSON><PERSON> g<PERSON> nói", "voiceTypes.favoriteVoices": "<PERSON><PERSON><PERSON><PERSON> nói yêu th<PERSON>ch", "voiceTypes.geminiVoices": "Giọng nói Gemini", "voiceTypes.systemVoices": "<PERSON><PERSON><PERSON><PERSON> nói hệ thống", "voiceTypes.customVoices": "<PERSON><PERSON><PERSON><PERSON> nói tùy chỉnh", "voiceTypes.premiumVoices": "<PERSON><PERSON><PERSON><PERSON> nói cao cấp", "voiceTypes.userVoices": "<PERSON><PERSON><PERSON>ng nói người dùng", "demo.speechVoiceSelect.title": "<PERSON><PERSON> Chọn Giọng Nó<PERSON>", "demo.speechVoiceSelect.description": "Demonstrating component BaseSpeechVoiceSelectModal với modelValue props", "demo.notifications.title": "Loại Thông Báo & Trạng Thái Demo", "demo.notifications.description": "<PERSON><PERSON> dụ về các loại thông báo khác nhau với các trạng thái khác nhau.", "demo.notifications.statusLegend": "<PERSON><PERSON><PERSON><PERSON> thái huyền tho<PERSON>i", "listenToSpeech": "<PERSON><PERSON> bài phát bi<PERSON>u", "generateSimilar": "<PERSON><PERSON><PERSON> t<PERSON> tự", "aspectRatio": "Tỷ l<PERSON> khung hình", "Image Reference": "<PERSON><PERSON> k<PERSON><PERSON><PERSON> h<PERSON>", "safety_filter_level": "<PERSON><PERSON><PERSON>", "used_credit": "<PERSON>ín dụng đã sử dụng", "Safety Filter": "<PERSON><PERSON> lọc an toàn", "Person Generation": "<PERSON><PERSON><PERSON> n<PERSON>i dung có người", "downloadImage": "<PERSON><PERSON><PERSON> xu<PERSON> h<PERSON>nh <PERSON>", "noImageAvailable": "<PERSON><PERSON><PERSON>ng có hình ảnh nào có sẵn", "enhancePrompt": "<PERSON><PERSON>ng cường gợi ý", "addImages": "<PERSON><PERSON><PERSON><PERSON>", "generateVideo": "Tạo video", "happy": "<PERSON><PERSON><PERSON> p<PERSON>c", "sad": "<PERSON><PERSON><PERSON><PERSON>", "angry": "<PERSON><PERSON><PERSON>", "excited": "<PERSON><PERSON><PERSON>", "laughing": "Cười", "crying": "<PERSON><PERSON><PERSON><PERSON>", "calm": "<PERSON><PERSON><PERSON> tĩnh", "serious": "<PERSON><PERSON><PERSON><PERSON>", "frustrated": "<PERSON><PERSON><PERSON>", "hopeful": "<PERSON><PERSON> v<PERSON>ng", "narrative": "<PERSON><PERSON><PERSON><PERSON>", "kids' storytelling": "<PERSON><PERSON><PERSON> chuyện kể của trẻ em", "audiobook": "<PERSON><PERSON><PERSON>", "poetic": "<PERSON><PERSON><PERSON> mộng", "mysterious": "<PERSON><PERSON> <PERSON><PERSON>", "inspirational": "<PERSON><PERSON><PERSON><PERSON><PERSON> cả<PERSON> h<PERSON>", "surprised": "<PERSON><PERSON><PERSON>", "confident": "Tự tin", "romantic": "<PERSON><PERSON><PERSON> m<PERSON>n", "scared": "<PERSON><PERSON> hãi", "trailer voice": "<PERSON><PERSON><PERSON><PERSON>", "advertising": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>o", "documentary": "<PERSON><PERSON> tài li<PERSON>u", "newsreader": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> tin tức", "weather report": "<PERSON><PERSON> báo thời tiết", "game commentary": "<PERSON><PERSON><PERSON> luận trò ch<PERSON>i", "interactive": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "customer support": "Hỗ trợ khách hàng", "playful": "<PERSON><PERSON>", "tired": "Mệt mỏi", "sarcastic": "<PERSON><PERSON><PERSON>", "disgusted": "G<PERSON><PERSON> tởm", "whispering": "<PERSON><PERSON><PERSON> thầm", "persuasive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nostalgic": "<PERSON><PERSON><PERSON>", "meditative": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "announcement": "<PERSON><PERSON><PERSON><PERSON> báo", "professional pitch": "<PERSON><PERSON><PERSON> thuyết trình chuyên ng<PERSON>", "casual": "<PERSON><PERSON><PERSON>", "exciting trailer": "Đoạn trailer thú vị", "dramatic": "<PERSON><PERSON><PERSON>", "corporate": "<PERSON><PERSON><PERSON>", "tech enthusiast": "<PERSON><PERSON><PERSON><PERSON> đam mê công nghệ", "youthful": "Trẻ trung", "calming reassurance": "<PERSON><PERSON><PERSON><PERSON> an dịu dàng", "heroic": "<PERSON><PERSON> h<PERSON>", "festive": "<PERSON><PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p", "motivational": "<PERSON><PERSON><PERSON><PERSON><PERSON> cả<PERSON> h<PERSON>", "friendly": "<PERSON><PERSON><PERSON> th<PERSON>", "energetic": "<PERSON><PERSON><PERSON> động", "serene": "<PERSON><PERSON> b<PERSON>nh", "bold": "Đậm", "charming": "Hấp dẫn", "monotone": "<PERSON><PERSON><PERSON> đi<PERSON>", "questioning": "<PERSON><PERSON><PERSON> v<PERSON>n", "directive": "Chỉ thị", "dreamy": "M<PERSON> mộng", "epic": "<PERSON><PERSON> thi", "lyrical": "<PERSON><PERSON><PERSON> tình", "mystical": "<PERSON><PERSON><PERSON><PERSON> bí", "melancholy": "<PERSON> sầu", "cheerful": "<PERSON><PERSON> vẻ", "eerie": "<PERSON><PERSON><PERSON> r<PERSON>n", "flirtatious": "<PERSON><PERSON> vãn", "thoughtful": "<PERSON>o", "cinematic": "<PERSON><PERSON><PERSON><PERSON>", "humorous": "<PERSON><PERSON><PERSON>", "instructional": "Hướng dẫn", "conversational": "<PERSON><PERSON><PERSON>", "apologetic": "Xin lỗi", "excuse-making": "Viện cớ", "encouraging": "<PERSON><PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch", "neutral": "<PERSON><PERSON> lập", "authoritative": "<PERSON><PERSON><PERSON> tin cậy", "sarcastic cheerful": "Mỉa mai hớn hở", "reassuring": "<PERSON><PERSON><PERSON><PERSON> an", "formal": "<PERSON><PERSON> trọng", "anguished": "<PERSON><PERSON><PERSON> đau", "giggling": "Cười khúc khích", "exaggerated": "<PERSON><PERSON><PERSON>", "cold": "Lạnh", "hot-tempered": "<PERSON><PERSON><PERSON>", "grateful": "<PERSON><PERSON><PERSON><PERSON>", "regretful": "<PERSON><PERSON><PERSON>", "provocative": "<PERSON><PERSON><PERSON><PERSON>", "triumphant": "<PERSON><PERSON><PERSON> th<PERSON>ng", "vengeful": "<PERSON><PERSON><PERSON>", "heroic narration": "<PERSON><PERSON><PERSON><PERSON> thu<PERSON>t anh hùng", "villainous": "<PERSON><PERSON>u xa", "hypnotic": "<PERSON><PERSON><PERSON><PERSON>n", "desperate": "Tuyệt vọng", "lamenting": "Than thở", "celebratory": "<PERSON><PERSON><PERSON> m<PERSON>", "teasing": "<PERSON><PERSON><PERSON><PERSON>", "exhausted": "<PERSON><PERSON><PERSON> s<PERSON>", "questioning suspicious": "<PERSON><PERSON>t vấn nghi ngờ", "optimistic": "<PERSON><PERSON><PERSON> quan", "bright, gentle voice, expressing excitement.": "<PERSON><PERSON><PERSON><PERSON> nói trong trẻo, nhẹ nh<PERSON><PERSON>, thể hiện sự phấn khích.", "low, slow voice, conveying deep emotions.": "<PERSON><PERSON><PERSON><PERSON> trầm, <PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON> tải cảm xúc sâu sắc.", "sharp, exaggerated voice, expressing frustration.": "<PERSON><PERSON><PERSON><PERSON> điệu nhấn mạnh, p<PERSON><PERSON><PERSON> đại, bi<PERSON><PERSON> lộ sự thất vọng.", "fast, lively voice, full of enthusiasm.": "<PERSON><PERSON><PERSON><PERSON> nói n<PERSON> nhẹn, s<PERSON><PERSON> động, trà<PERSON> đ<PERSON><PERSON> n<PERSON> huy<PERSON>.", "interrupted, joyful voice, interspersed with laughter.": "<PERSON><PERSON><PERSON><PERSON> nói vui vẻ bị ng<PERSON>t quãng, xen lẫn tiếng cười.", "shaky, low voice, expressing pain.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> run r<PERSON><PERSON>, tr<PERSON><PERSON> thấp, thể hiện sự đau đớn.", "gentle, steady voice, providing reassurance.": "<PERSON><PERSON><PERSON><PERSON> nói nhẹ nhàng, đ<PERSON><PERSON> đặn, mang lại sự yên tâm.", "mature, clear voice, suitable for formal content.": "<PERSON><PERSON><PERSON><PERSON> trưởng thành, <PERSON><PERSON> ràng, phù hợp với nội dung trang trọng.", "weary, slightly irritated voice.": "<PERSON><PERSON><PERSON><PERSON> mệt mỏi, h<PERSON><PERSON> kh<PERSON> ch<PERSON>.", "bright voice, conveying positivity and hope.": "<PERSON><PERSON><PERSON><PERSON> nói rạng rỡ, truyền tải sự tích cực và hy vọng.", "natural, gentle voice with a slow rhythm.": "<PERSON><PERSON><PERSON><PERSON> nói tự nhiên, nhẹ nhàng với nhịp điệu chậm rãi.", "lively, engaging voice, captivating for children.": "<PERSON><PERSON><PERSON><PERSON> nói sống động, l<PERSON><PERSON> cu<PERSON>, hấp dẫn đối với trẻ em.", "even, slow voice, emphasizing content meaning.": "<PERSON><PERSON><PERSON><PERSON> nói đều đặn, ch<PERSON><PERSON>, nh<PERSON><PERSON> mạnh ý nghĩa nội dung.", "rhythmic, emotional voice, conveying subtlety.": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON><PERSON>, đ<PERSON><PERSON> c<PERSON>, t<PERSON><PERSON><PERSON><PERSON> tải nh<PERSON>ng nét tinh tế.", "low, slow voice, evoking curiosity.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> tr<PERSON>, ch<PERSON><PERSON>, kh<PERSON><PERSON> gợi sự tò mò.", "strong, passionate voice, driving action.": "<PERSON><PERSON><PERSON><PERSON> nói mạnh mẽ, đ<PERSON> mê, thúc đẩy hành động.", "high, interrupted voice, expressing astonishment.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>u<PERSON>, thể hiện sự kinh ngạc.", "firm, powerful voice, persuasive and assuring.": "<PERSON><PERSON><PERSON><PERSON> nó<PERSON> chắ<PERSON> chắn, mạnh mẽ, thuy<PERSON><PERSON> phục và đáng tin cậy.", "sweet, gentle voice, suitable for emotional content.": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> ng<PERSON>, nhẹ n<PERSON><PERSON><PERSON>, phù hợp với nội dung cảm xúc.", "shaky, interrupted voice, conveying anxiety.": "<PERSON><PERSON><PERSON><PERSON> run r<PERSON><PERSON>, ng<PERSON><PERSON> qu<PERSON>, t<PERSON><PERSON><PERSON><PERSON> đ<PERSON>t sự lo lắng.", "deep, strong voice with emphasis, creating suspense.": "<PERSON><PERSON><PERSON><PERSON> sâ<PERSON>, mạnh mẽ với sự nhấn nhá, t<PERSON><PERSON> ra sự hồi hộp.", "engaging, lively voice, emphasizing product benefits.": "<PERSON><PERSON><PERSON><PERSON> nó<PERSON> lôi cuốn, <PERSON><PERSON><PERSON> n<PERSON>, nh<PERSON><PERSON> mạnh lợi ích của sản phẩm.", "formal, clear voice with focus on key points.": "<PERSON><PERSON><PERSON><PERSON> nói trang trọng, rõ ràng với sự tập trung vào các điểm ch<PERSON>.", "calm, profound voice, delivering authenticity.": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u tr<PERSON><PERSON>, <PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON> tải sự chân thực.", "standard, neutral voice, clear and precise.": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON><PERSON>, t<PERSON> lậ<PERSON>, rõ ràng và ch<PERSON>h x<PERSON>c.", "bright, neutral voice, suitable for concise updates.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>h<PERSON> hợp cho các cập nhật ngắn gọn.", "fast, lively voice, stimulating excitement.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON> đ<PERSON>, k<PERSON>ch thích sự hứng khởi.", "friendly, approachable voice, encouraging engagement.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> thân th<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch sự tham gia.", "empathetic, gentle voice, easy to connect with.": "<PERSON><PERSON><PERSON><PERSON> nói g<PERSON><PERSON>, nhẹ n<PERSON><PERSON>, d<PERSON> dàng kết n<PERSON>i.", "clear voice, emphasizing questions and answers.": "<PERSON><PERSON><PERSON><PERSON> nói rõ ràng, nhấn mạnh vào câu hỏi và câu trả lời.", "cheerful, playful voice with a hint of mischief.": "<PERSON><PERSON><PERSON><PERSON> nói vui vẻ, tinh nghịch với chút nghịch ngợm.", "slow, soft voice lacking energy.": "<PERSON><PERSON><PERSON><PERSON> nó<PERSON>, nhẹ nhàng thiếu năng lượng.", "ironic, sharp voice, sometimes humorous.": "<PERSON><PERSON><PERSON><PERSON> châm bi<PERSON>, <PERSON><PERSON><PERSON> b<PERSON>, đ<PERSON><PERSON> khi hài hư<PERSON>.", "cold voice, clearly expressing discomfort.": "<PERSON><PERSON><PERSON><PERSON> lạnh lùng, thể hiện rõ sự khó chịu.", "soft, mysterious voice, creating intimacy.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> mềm m<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, t<PERSON>o s<PERSON> gần gũi.", "emotional voice, convincing the listener to act.": "<PERSON><PERSON><PERSON><PERSON> nói đ<PERSON> c<PERSON>, thuy<PERSON><PERSON> phụ<PERSON> người nghe hành động.", "gentle voice, evoking feelings of reminiscence.": "<PERSON><PERSON><PERSON><PERSON> nói dịu dà<PERSON>, g<PERSON>i nhớ về những ký <PERSON>.", "even, relaxing voice, suitable for mindfulness.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> đều đặn, <PERSON><PERSON>, phù hợ<PERSON> cho ch<PERSON>.", "clear voice, emphasizing key words.": "<PERSON><PERSON><PERSON><PERSON> nói rõ ràng, nh<PERSON>n mạnh các từ khóa.", "confident, clear voice, ideal for business presentations.": "<PERSON><PERSON><PERSON><PERSON> nói tự tin, <PERSON><PERSON> r<PERSON>, lý tưởng cho các buổi thuyết trình kinh doanh.", "natural, friendly voice, as if talking to a friend.": "<PERSON><PERSON><PERSON><PERSON> nó<PERSON> tự <PERSON>, th<PERSON> th<PERSON>, n<PERSON><PERSON> thể đang nói chuyện với một người bạn.", "fast, powerful voice, creating tension and excitement.": "<PERSON><PERSON><PERSON>, mạnh mẽ g<PERSON><PERSON><PERSON>, tạo ra căng thẳng và hưng phấn.", "emphasized, suspenseful voice, creating intensity.": "<PERSON><PERSON><PERSON><PERSON><PERSON> mạnh, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, t<PERSON><PERSON> ra c<PERSON><PERSON><PERSON> đ<PERSON>.", "professional, formal voice, suitable for business content.": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON><PERSON> chuy<PERSON>, trang tr<PERSON>, <PERSON><PERSON><PERSON> hợp cho nội dung kinh doanh.", "energetic, lively voice, introducing new technologies.": "<PERSON><PERSON><PERSON><PERSON> nói tràn đ<PERSON>y n<PERSON>ng <PERSON>, s<PERSON><PERSON> nổ<PERSON>, gi<PERSON>i thiệu các công nghệ mới.", "vibrant, cheerful voice, appealing to younger audiences.": "<PERSON><PERSON><PERSON><PERSON> vui t<PERSON>, <PERSON><PERSON><PERSON> đ<PERSON>, thu hút kh<PERSON> gi<PERSON> trẻ.", "gentle, empathetic voice, easing concerns.": "<PERSON><PERSON><PERSON><PERSON> nói nhẹ nhàng, đ<PERSON><PERSON> c<PERSON>, xoa d<PERSON><PERSON> lo l<PERSON>.", "strong, decisive voice, full of inspiration.": "<PERSON><PERSON><PERSON><PERSON> mạnh mẽ, quy<PERSON><PERSON>, đ<PERSON><PERSON> cả<PERSON> hứ<PERSON>.", "bright, excited voice, suitable for celebrations.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>h<PERSON> hợ<PERSON> cho các lễ kỷ niệm.", "fast, strong voice, emphasizing urgency.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON>, mạnh mẽ, nh<PERSON>n mạnh t<PERSON>h cấp bách.", "passionate, inspiring voice, encouraging action.": "<PERSON><PERSON><PERSON><PERSON> nói đầy đam mê, t<PERSON><PERSON><PERSON><PERSON> cả<PERSON> hứ<PERSON>, k<PERSON><PERSON><PERSON><PERSON> kh<PERSON><PERSON> hành động.", "warm, approachable voice, fostering connection.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, t<PERSON><PERSON> sự kết n<PERSON>.", "fast, powerful voice, brimming with enthusiasm.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> mạnh mẽ, tràn đ<PERSON><PERSON> n<PERSON><PERSON> huy<PERSON>.", "slow, gentle voice, evoking peace and tranquility.": "<PERSON><PERSON><PERSON><PERSON> nói chậm rãi, ê<PERSON> á<PERSON>, gợ<PERSON> lên sự bình yên và tĩnh lặng.", "firm, assertive voice, exuding confidence.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, q<PERSON><PERSON> quy<PERSON>, to<PERSON><PERSON> lên sự tự tin.", "warm, captivating voice, leaving a strong impression.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON>, l<PERSON><PERSON>, để lại ấn tượng mạnh mẽ.", "flat, unvaried voice, conveying neutrality or irony.": "<PERSON><PERSON><PERSON><PERSON> điệu đều đều, kh<PERSON><PERSON> thay đổi, thể hiện sự trung lập hoặc mỉa mai.", "curious voice, emphasizing questions.": "<PERSON><PERSON><PERSON><PERSON> tò mò, nhấn mạnh các câu hỏi.", "firm, clear voice, guiding the listener step-by-step.": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON><PERSON>, <PERSON><PERSON>, hư<PERSON><PERSON> dẫn người nghe từng bước một.", "gentle, slow voice, evoking a floating sensation.": "<PERSON><PERSON><PERSON><PERSON> nói nhẹ nhàng, ch<PERSON><PERSON>, gợ<PERSON> lên cảm giác bay bổng.", "deep, resonant voice, emphasizing grandeur.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, van<PERSON>, <PERSON><PERSON><PERSON><PERSON> mạnh sự hùng vĩ.", "soft, melodic voice, similar to singing.": "<PERSON><PERSON><PERSON><PERSON> nói nhẹ nhàng, du <PERSON>, gi<PERSON><PERSON> nh<PERSON> đang h<PERSON>t.", "low, drawn-out voice, evoking mystery.": "<PERSON><PERSON><PERSON><PERSON> thấ<PERSON>, k<PERSON><PERSON> d<PERSON>, g<PERSON><PERSON> lên sự b<PERSON> <PERSON>n.", "slow, low voice, conveying deep sadness.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> ch<PERSON>, t<PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON>n tải nỗi buồn sâu sắc.", "bright, energetic voice, full of positivity.": "<PERSON><PERSON><PERSON><PERSON> nó<PERSON> s<PERSON> s<PERSON>, <PERSON><PERSON><PERSON> đ<PERSON>, đ<PERSON><PERSON> t<PERSON><PERSON> c<PERSON>.", "low, whispery voice, evoking fear or strangeness.": "<PERSON><PERSON><PERSON><PERSON> nói nhỏ, th<PERSON> thầ<PERSON>, kh<PERSON><PERSON> gợi nỗi sợ hãi hoặc sự kỳ lạ.", "sweet, teasing voice, full of allure.": "<PERSON><PERSON><PERSON><PERSON> nói ng<PERSON> ng<PERSON>o, t<PERSON><PERSON><PERSON> ghẹo, đ<PERSON><PERSON> quyến rũ.", "slow, reflective voice, full of contemplation.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ch<PERSON>, t<PERSON><PERSON><PERSON>, đ<PERSON><PERSON> tư.", "resonant, emphasized voice, creating a movie-like effect.": "<PERSON><PERSON><PERSON><PERSON> vang <PERSON>, <PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON> hi<PERSON>u <PERSON>ng nh<PERSON> trong phim.", "lighthearted, cheerful voice, sometimes exaggerated.": "<PERSON><PERSON><PERSON><PERSON> vui t<PERSON>, phấn khởi, đ<PERSON><PERSON> lúc có phần phóng đại.", "clear, slow voice, guiding the listener step-by-step.": "<PERSON><PERSON><PERSON><PERSON> rõ rà<PERSON>, ch<PERSON><PERSON>, h<PERSON><PERSON><PERSON> dẫn người nghe từng bước một.", "natural voice, as if chatting with the listener.": "<PERSON><PERSON><PERSON><PERSON> nói tự <PERSON>, nh<PERSON> thể đang trò chuyện với người nghe.", "soft, sincere voice, expressing regret.": "<PERSON><PERSON><PERSON><PERSON> nói nhẹ nhàng, <PERSON><PERSON> thành, thể hiện sự hối tiếc.", "hesitant, uncertain voice, sometimes awkward.": "<PERSON><PERSON><PERSON><PERSON> nó<PERSON> do dự, <PERSON><PERSON><PERSON><PERSON> chắ<PERSON> chắ<PERSON>, đ<PERSON><PERSON> khi vụng về.", "warm voice, providing motivation and support.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>, mang lại động lực và sự hỗ trợ.", "even voice, free of emotional bias.": "<PERSON><PERSON><PERSON><PERSON> nói đồng đều, kh<PERSON><PERSON> thiên l<PERSON>ch cảm x<PERSON>c.", "strong, powerful voice, exuding credibility.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> mạnh mẽ, <PERSON><PERSON><PERSON> uy l<PERSON>, to<PERSON><PERSON> lên sự uy tín.", "cheerful voice with an undertone of mockery.": "<PERSON><PERSON><PERSON><PERSON> nói vui tươi với sự giễu cợt ng<PERSON>m.", "gentle, empathetic voice, providing comfort.": "<PERSON><PERSON><PERSON><PERSON> nói nhẹ nhàng, đ<PERSON><PERSON> c<PERSON>, mang lại s<PERSON> an <PERSON>.", "clear, polite voice, suited for formal occasions.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON>, <PERSON><PERSON><PERSON>, phù hợp cho các dịp trang trọng.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON><PERSON> g<PERSON>, <PERSON><PERSON><PERSON><PERSON> run r<PERSON>, bi<PERSON><PERSON> lộ sự đau khổ.", "interrupted voice, mixed with light laughter.": "<PERSON><PERSON><PERSON><PERSON> nói bị ngắt quãng, pha lẫn với tiếng cười nhẹ.", "loud, emphasized voice, often humorous.": "<PERSON><PERSON><PERSON><PERSON> nói to, nh<PERSON><PERSON> mạnh, thư<PERSON><PERSON> hài hước.", "flat, unemotional voice, conveying detachment.": "<PERSON><PERSON><PERSON><PERSON> đều đều, th<PERSON><PERSON><PERSON> c<PERSON>, t<PERSON><PERSON><PERSON><PERSON> tải sự thờ ơ.", "fast, sharp voice, sometimes out of control.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> khi mất kiểm so<PERSON>t.", "warm, sincere voice, expressing appreciation.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> th<PERSON>nh, thể hiện sự trân trọng.", "low, subdued voice, full of remorse.": "<PERSON><PERSON><PERSON><PERSON> trầm, nhỏ nhẹ, <PERSON><PERSON><PERSON> h<PERSON><PERSON> hận.", "challenging, strong voice, full of insinuation.": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> mạnh mẽ, <PERSON><PERSON><PERSON> <PERSON><PERSON>.", "loud, powerful voice, full of victory.": "<PERSON><PERSON><PERSON><PERSON> nói vang d<PERSON>, mạ<PERSON> mẽ, tràn đ<PERSON><PERSON> chiến thắng.", "low, cold voice, expressing determination for revenge.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> trầ<PERSON>, <PERSON><PERSON><PERSON>ù<PERSON>, thể hiện quyết tâm trả thù.", "strong, inspiring voice, emphasizing heroic deeds.": "<PERSON><PERSON><PERSON><PERSON> nói mạnh mẽ, t<PERSON><PERSON><PERSON><PERSON> cảm hứng, nh<PERSON>n mạnh những hành động anh hùng.", "low, drawn-out voice, full of scheming.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tr<PERSON>, <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> mưu mô.", "even, repetitive voice, drawing the listener in.": "<PERSON><PERSON><PERSON><PERSON> điệu đều đặn, lặp đi lặp lại, thu hút người nghe.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p, <PERSON><PERSON><PERSON><PERSON> run r<PERSON><PERSON>, thể hiện sự tuyệt vọng.", "low, sorrowful voice, as if mourning.": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON>, bu<PERSON><PERSON> bã, nh<PERSON> thể đang than thở.", "excited, joyful voice, full of festive spirit.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> h<PERSON>, <PERSON><PERSON>, trà<PERSON> đ<PERSON><PERSON> tinh thần l<PERSON> hội.", "light, playful voice, sometimes mockingly.": "<PERSON><PERSON><PERSON><PERSON> nhẹ nh<PERSON>ng, v<PERSON>, đ<PERSON><PERSON> khi chế gi<PERSON>.", "weak, broken voice, expressing extreme fatigue.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> qu<PERSON>, bộ<PERSON> lộ sự mệt mỏi tột độ.", "slow, emphasized voice, full of suspicion.": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ch<PERSON>, <PERSON><PERSON><PERSON><PERSON>, đ<PERSON><PERSON> nghi ng<PERSON>.", "bright, hopeful voice, creating positivity.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> t<PERSON><PERSON>, <PERSON><PERSON><PERSON> hy v<PERSON>, t<PERSON><PERSON> ra s<PERSON> tích cực.", "Your audio will be processed with the latest stable model.": "<PERSON><PERSON> <PERSON>h của bạn sẽ được xử lý bằng mô hình ổn định mới nhất.", "Your audio will be processed with the latest beta model.": "<PERSON><PERSON> <PERSON>h của bạn sẽ được xử lý bằng mô hình beta mới nhất.", "You don't have any saved prompts yet.": "Bạn chưa có lời nhắc nào đ<PERSON><PERSON><PERSON> lư<PERSON>.", "Commercial Use": "<PERSON><PERSON> dụng thư<PERSON><PERSON> mại", "Other people’s privacy": "<PERSON><PERSON><PERSON><PERSON> riêng tư của người khác", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "Bạn phải tôn trọng quyền riêng tư của người khác khi sử dụng dịch vụ của chúng tôi. Không tải lên hoặc tạo đầu ra giọng nói chứa thông tin cá nhân, dữ liệu bảo mật hoặc tài liệu có bản quyền mà không có sự cho phép.", "{price}$ per credit": "{price}$ mỗi Credits", "Pricing": "<PERSON><PERSON><PERSON> c<PERSON>", "Simple and flexible. Only pay for what you use.": "Đơn giản và linh ho<PERSON>t. Chỉ trả tiền cho những gì bạn sử dụng.", "Pay as you go": "<PERSON><PERSON><PERSON> bao nhiêu tr<PERSON> b<PERSON>y nhiêu", "Flexible": "<PERSON><PERSON>", "Input characters": "<PERSON>ý tự đầu vào", "Audio model": "<PERSON><PERSON> h<PERSON>nh <PERSON>h", "Credits": "<PERSON><PERSON> d<PERSON>", "Cost": "Chi phí", "HD quality voices": "Giọng nói chất l<PERSON>", "Advanced model": "<PERSON><PERSON> hình tiên tiến", "Buy now": "<PERSON><PERSON> ngay", "Paste your text to calculate": "<PERSON><PERSON> văn bản của bạn để tính toán", "Paste your text here...": "<PERSON><PERSON> văn bản của bạn vào đây...", "Calculate": "<PERSON><PERSON><PERSON>", "Estimate your cost by drag the slider below or": "Ước tính chi phí của bạn bằng cách kéo thanh trượt bên dưới hoặc", "calming": "Êm dịu", "customer": "<PERSON><PERSON><PERSON><PERSON>", "exciting": "thú vị", "excuse": "Xin lỗi", "game": "<PERSON><PERSON><PERSON>", "hot": "Nóng", "kids": "Trẻ em", "professional": "<PERSON><PERSON><PERSON><PERSON>", "tech": "<PERSON><PERSON><PERSON> nghệ", "trailer": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "weather": "<PERSON><PERSON><PERSON><PERSON> tiết", "No thumbnail available": "<PERSON><PERSON><PERSON><PERSON> có hình", "Debit or Credit Card": "Thẻ ghi nợ hoặc thẻ tín dụng", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express và nhiều hơn nữa", "Top up now": "<PERSON><PERSON><PERSON> ngay bây giờ", "noVideoAvailable": "Không có video nào có sẵn", "voice 1": "Giọng nói 1", "voice 2": "Giọng nói 2", "uuid": "UUID", "output_format": "<PERSON><PERSON><PERSON> dạng xuất", "output_channel": "<PERSON><PERSON><PERSON>", "file_name": "<PERSON><PERSON><PERSON>", "file_size": "<PERSON><PERSON><PERSON>", "speakers_count": "Số l<PERSON> ng<PERSON> nói", "custom_prompt": "Gợi ý tùy chỉnh", "tts-document": "Audio", "assignVoicesToSpeakers": "<PERSON><PERSON> g<PERSON>ng nói cho người nói", "speakers": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i", "addSpeaker": "<PERSON><PERSON><PERSON><PERSON> nói", "noVoiceAssigned": "Chưa gán giọng nói", "noSpeakersAdded": "<PERSON><PERSON><PERSON> thêm ng<PERSON>ời nói nào", "assignVoiceToSpeaker": "<PERSON><PERSON> g<PERSON> nói cho {speaker}", "assigned": "Đã gán", "assign": "Gán", "editSpeaker": "Chỉnh sửa người nói", "speakerName": "<PERSON><PERSON><PERSON> n<PERSON>i", "enterSpeakerName": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nói", "save": "<PERSON><PERSON><PERSON>", "speaker": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i", "assignVoices": "<PERSON><PERSON> g<PERSON> nói", "speakersWithVoices": "{assigned}/{total} ngư<PERSON>i nói có giọng nói", "dialogs": "<PERSON><PERSON><PERSON> tho<PERSON>i", "addDialog": "<PERSON><PERSON><PERSON><PERSON> hội tho<PERSON>i", "enterDialogText": "<PERSON><PERSON><PERSON><PERSON> văn bản hội thoại...", "selectSpeaker": "<PERSON><PERSON><PERSON> ng<PERSON> nói", "generateDialogSpeech": "<PERSON><PERSON>o g<PERSON> nói hội thoại", "Please wait a moment...": "<PERSON>ui lòng đợi một chút...", "Click to copy": "<PERSON><PERSON><PERSON><PERSON> để sao chép", "Copied to clipboard": "Đã sao chép vào clipboard", "UUID has been copied to clipboard": "UUID đã đư<PERSON>c sao chép vào clipboard", "Regenerate Image": "<PERSON><PERSON><PERSON> lạ<PERSON>", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "Bạn chưa thay đổi bất kỳ cài đặt nào. Bạn có chắc chắn muốn tạo lại cùng một ảnh không?", "Yes, Regenerate": "Có, tạo lại", "Cancel": "<PERSON><PERSON><PERSON>", "common.back": "Trở lại", "common.edit": "Chỉnh sửa", "common.save": "<PERSON><PERSON><PERSON>", "common.cancel": "<PERSON><PERSON><PERSON>", "common.delete": "Xóa", "common.copy": "Sao chép", "common.copied": "Đã sao chép", "common.manage": "<PERSON><PERSON><PERSON><PERSON> lý", "aiToolMenu.textToImage": "<PERSON>y<PERSON><PERSON> văn bản thành hình <PERSON>nh", "profileMenu.integration": "<PERSON><PERSON><PERSON>", "videoTypes.examples.tikTokDanceTrend": "<PERSON>ik<PERSON>", "videoTypes.examples.energeticDanceDescription": "Video nhảy sôi động với màu sắc rực rỡ, c<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, nh<PERSON><PERSON> thịnh hành, định dạng dọc, phong cách mạng xã hội.", "videoTypes.examples.instagramReel": "Instagram Reel", "videoTypes.examples.lifestyleDescription": "<PERSON><PERSON><PERSON> dung phong cách sống với hình ảnh thẩm mỹ, chuy<PERSON><PERSON> cảnh mư<PERSON><PERSON> mà, hi<PERSON><PERSON> <PERSON><PERSON> thịnh hành, k<PERSON> chuyện hấp dẫn.", "videoTypes.examples.comedySketch": "<PERSON><PERSON><PERSON><PERSON> phẩm hài k<PERSON>ch", "videoTypes.examples.funnyComedyDescription": "<PERSON><PERSON><PERSON> hài kịch vui nhộn với các nhân vật bi<PERSON><PERSON> cả<PERSON>, tình huống hà<PERSON> hư<PERSON>, đối tho<PERSON>i gi<PERSON> trí, tâm trạng nhẹ nhàng.", "videoTypes.examples.productLaunchAd": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>o ra mắt sản phẩm", "videoTypes.examples.professionalCorporateDescription": "Video doanh nghiệp chuyên nghiệp với phần trình bày của gi<PERSON>m đốc, môi trường văn phòng sạch sẽ, phong cách trang trọng trong kinh doanh.", "videoTypes.examples.quickPromoVideo": "Video Quảng <PERSON>g", "videoTypes.examples.fastPacedPromoDescription": "<PERSON><PERSON>i dung quảng cáo nhanh với sản xuất hiệu quả, h<PERSON>nh <PERSON>nh tiết kiệm chi phí, thông đi<PERSON><PERSON> tinh gọn.", "videoTypes.examples.birthdayGreeting": "<PERSON><PERSON><PERSON> ch<PERSON>c mừng sinh nh<PERSON>t", "videoTypes.examples.personalizedBirthdayDescription": "Video sinh nhật cá nhân hóa với trang trí lễ hội, <PERSON><PERSON> <PERSON><PERSON>, khô<PERSON> k<PERSON><PERSON> chú<PERSON> mừng, thông đi<PERSON><PERSON> chân thành.", "videoTypes.examples.brandStoryVideo": "Video Câu <PERSON>", "videoTypes.examples.tutorialVideo": "Video hướng dẫn", "videoTypes.examples.manOnThePhone": "<PERSON><PERSON><PERSON>i đàn ông đang gọi điện thoại", "videoTypes.examples.runningSnowLeopard": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON>", "videoTypes.examples.snowLeopard": "<PERSON><PERSON><PERSON>", "videoTypes.styles.cartoonAnimated": "Video phong cách hoạt hình hoặc hoạt họa", "videoTypes.styles.naturalDocumentary": "Cảnh quay phong cách tài liệu tự nhiên", "videoTypes.styles.naturalLifelike": "<PERSON><PERSON> cách video tự nhiên, sống động nh<PERSON> thật", "videoTypes.styles.professionalMovieQuality": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> phim chuyên nghiệp với ánh sáng kịch t<PERSON>h", "videoTypes.styles.creativeStylized": "<PERSON><PERSON><PERSON>ng video sáng tạo và phong cách", "videoTypes.styles.retroVintage": "<PERSON><PERSON> cách thẩm mỹ video cổ điển hoặc hoài cổ", "demo.notifications.availableNotificationTypes": "<PERSON><PERSON><PERSON> lo<PERSON>i thông báo có sẵn", "demo.speechVoiceSelect.example1": "Ví d<PERSON> 1: <PERSON><PERSON><PERSON> Mặc Định", "demo.speechVoiceSelect.example2": "<PERSON><PERSON> 2: <PERSON><PERSON><PERSON> nhỏ", "demo.speechVoiceSelect.example3": "Ví d<PERSON> 3: <PERSON><PERSON><PERSON> lớn", "demo.speechVoiceSelect.example4": "Ví dụ 4: <PERSON><PERSON><PERSON><PERSON> <PERSON>í Dụ Lỗi", "demo.speechVoiceSelect.example5": "<PERSON><PERSON> 5: <PERSON> s<PERSON>h <PERSON>ng thái", "demo.speechVoiceSelect.mainNarrator": "Người dẫn chuyện ch<PERSON>h", "demo.speechVoiceSelect.characterVoice": "Giọng Nhân Vật", "demo.speechVoiceSelect.selectedVoicesSummary": "<PERSON><PERSON>m tắt Các Giọng Nói Đã Ch<PERSON>:", "demo.speechVoiceSelect.clearAll": "<PERSON><PERSON><PERSON> tất cả", "demo.speechVoiceSelect.setRandomVoices": "Đặt Giọng Nói Ngẫu Nhiên", "demo.speechVoiceSelect.logToConsole": "<PERSON><PERSON> <PERSON>h<PERSON>t ký vào bảng điều khiển", "demo.speechVoiceSelect.notSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n", "demo.speechVoiceSelect.voiceSelectionsChanged": "<PERSON><PERSON> thay đổi lựa chọn gi<PERSON>ng nói", "demo.historyWrapper.title": "De<PERSON> huy hiệu trạng thái của HistoryWrapper", "demo.historyWrapper.normalStatus": "Ví dụ 1: <PERSON><PERSON><PERSON><PERSON> thái <PERSON> (trạng thái = 1)", "demo.historyWrapper.processingStatus": "<PERSON><PERSON> dụ 2: <PERSON><PERSON><PERSON><PERSON> thái <PERSON>ý (trạng thái = 2)", "demo.historyWrapper.errorStatus": "<PERSON>í dụ 3: Tr<PERSON>ng thái Lỗi (trạng thái = 3) - <PERSON><PERSON><PERSON> thị Biểu tượng Lỗi", "demo.historyWrapper.multipleErrorExamples": "Ví dụ 4: <PERSON><PERSON><PERSON><PERSON> ví dụ sai sót", "demo.historyWrapper.statusComparison": "<PERSON><PERSON> 5: So s<PERSON>h trạng thái", "demo.historyWrapper.normalImageGeneration": "<PERSON><PERSON><PERSON> Ảnh Thông Thường", "demo.historyWrapper.videoGenerationInProgress": "<PERSON><PERSON> trong quá trình tạo video", "demo.historyWrapper.speechGenerationFailed": "<PERSON><PERSON>o g<PERSON>ng nói thất bại", "demo.historyWrapper.imageFailed": "<PERSON><PERSON><PERSON>nh không thành công", "demo.historyWrapper.videoFailed": "Video không thành công", "demo.historyWrapper.speechFailed": "<PERSON><PERSON><PERSON><PERSON> thể phát biểu", "demo.historyWrapper.statusSuccess": "Trạng thái: <PERSON><PERSON><PERSON><PERSON> công", "demo.historyWrapper.statusProcessing": "<PERSON>rạng thái: <PERSON><PERSON> lý", "demo.historyWrapper.statusError": "Trạng thái: Lỗi", "demo.historyWrapper.status1Success": "Trạng thái 1: <PERSON><PERSON><PERSON><PERSON> công", "demo.historyWrapper.status2Processing": "<PERSON>rạng thái 2: <PERSON><PERSON>ý", "demo.historyWrapper.badgeBehavior": "<PERSON><PERSON><PERSON> vi phù hiệu:", "demo.historyWrapper.showsOnlyTypeAndStyle": "Chỉ hiển thị các huy hiệu loại và kiểu dáng", "demo.historyWrapper.showsTypeStyleAndError": "Hi<PERSON><PERSON> thị loại, k<PERSON><PERSON><PERSON>, và huy hiệu lỗi màu đỏ với biểu tượng cảnh báo", "demo.historyWrapper.redBackgroundWithWhite": "Nền đỏ với chữ trắng và biểu tượng cảnh báo hình tròn", "demo.historyWrapper.allBadgesHideOnHover": "Tất cả huy hiệu ẩn khi di chuột để hiển thị nội dung phủ.", "demo.speechVoiceCaching.title": "<PERSON><PERSON><PERSON> tra Bộ nhớ Đệm G<PERSON>ọng Nói trong Lờ<PERSON>", "demo.speechVoiceCaching.description": "<PERSON>ểm tra để kiểm tra việc lưu trữ giọng nói giữa các thành phần khác nhau.", "demo.speechVoiceCaching.component1Modal": "<PERSON>hành phần 1 - <PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON>", "demo.speechVoiceCaching.component3RegularSelect": "<PERSON><PERSON>ành phần 3 - <PERSON><PERSON><PERSON><PERSON> Xuyên", "demo.speechVoiceCaching.forceReloadVoices": "<PERSON><PERSON>i L<PERSON>", "demo.speechVoiceCaching.clearAllSelections": "<PERSON><PERSON><PERSON>", "demo.speechVoiceCaching.logStoreState": "Tr<PERSON>ng thái lưu trữ nhật ký", "demo.speechVoiceCaching.refreshPageInstructions": "Tải lại trang và mở bất kỳ thành phần nào - sẽ tải lại", "demo.speechVoiceCaching.checkNetworkTab": "<PERSON><PERSON><PERSON> tra tab Mạng để xác nhận các cuộc g<PERSON> API", "demo.speechVoiceCaching.selectedVoicePersist": "Giọng nói đã chọn sẽ được lưu trữ qua localStorage.", "demo.speechVoiceCaching.pageMounted": "<PERSON><PERSON> đã tả<PERSON>, c<PERSON><PERSON> hàng sẽ tự động khởi tạo nếu cần thiết.", "integration.title": "<PERSON><PERSON><PERSON>", "integration.subtitle": "Q<PERSON>ản lý các khóa API và cài đặt tích hợp của bạn", "integration.apiKeys": "Khóa API", "integration.apiKeysDescription": "<PERSON><PERSON><PERSON>n lý các khóa API của bạn để truy cập lập trình", "integration.webhook": "Webhook", "integration.webhookDescription": "<PERSON><PERSON><PERSON> hình URL webhook cho thông báo", "apiKeys.title": "Các khóa API", "apiKeys.subtitle": "<PERSON><PERSON><PERSON>n lý các khóa API của bạn để truy cập theo lập trình", "apiKeys.create": "Tạo khóa API", "apiKeys.createNew": "Tạo Khóa API Mới", "apiKeys.createFirst": "Tạo Khóa API Đầu Tiên", "apiKeys.name": "<PERSON><PERSON><PERSON>", "apiKeys.nameDescription": "Đặt cho khóa API của bạn một tên mô tả", "apiKeys.namePlaceholder": "ví dụ: Khóa API Ứng dụng của tôi", "apiKeys.nameRequired": "Tên khóa API là bắt buộc.", "apiKeys.createdAt": "Đã tạo", "apiKeys.noKeys": "Không có khóa API", "apiKeys.noKeysDescription": "Tạo khóa API đầu tiên của bạn để bắt đầu truy cập theo lập trình.", "apiKeys.created": "Đã tạo khóa API thành công", "apiKeys.createError": "Không tạo đ<PERSON><PERSON> khó<PERSON> API", "apiKeys.deleted": "Đã xóa khóa API thành công", "apiKeys.deleteError": "Không thể xóa khóa API", "apiKeys.deleteConfirm": "Xóa khóa API", "apiKeys.deleteWarning": "Bạn có chắc chắn muốn xóa khóa API này không? Hành động này không thể hoàn tác.", "apiKeys.copied": "Khóa API đã được sao chép vào bộ nhớ tạm", "apiKeys.copyError": "Không thể sao chép khóa API", "webhook.title": "<PERSON><PERSON><PERSON> <PERSON>", "webhook.subtitle": "<PERSON><PERSON><PERSON> hình URL webhook cho thông báo thời gian thực", "webhook.configuration": "URL Webhook", "webhook.currentUrl": "URL Webhook hiện tại", "webhook.currentUrlDescription": "URL này sẽ nhận các yêu cầu POST cho các sự kiện webhook.", "webhook.notConfigured": "<PERSON><PERSON><PERSON>ng có URL webhook đ<PERSON><PERSON><PERSON> cấu hình", "webhook.url": "URL Webhook", "webhook.urlDescription": "Nhập URL nơi bạn muốn nhận thông báo webhook.", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "<PERSON><PERSON> lòng nhập URL webhook trước tiên.", "webhook.invalidUrl": "<PERSON><PERSON> lòng nhập URL hợp lệ", "webhook.saved": "<PERSON><PERSON> lưu thành công URL Webhook", "webhook.saveError": "<PERSON><PERSON><PERSON><PERSON> thể lưu URL webhook", "webhook.test": "<PERSON><PERSON><PERSON> tra", "webhook.testSent": "<PERSON><PERSON> gửi thử nghiệm", "webhook.testDescription": "<PERSON><PERSON> g<PERSON>i webhook kiểm tra thành công", "webhook.information": "Thông tin Webhook", "webhook.howItWorks": "<PERSON><PERSON><PERSON> ho<PERSON> động", "webhook.description": "Khi đã đư<PERSON><PERSON> cấu hình, chúng tôi sẽ gửi các yêu cầu HTTP POST tới URL webhook của bạn bất cứ khi nào có sự kiện nhất định xảy ra trong tài khoản của bạn.", "webhook.events": "<PERSON><PERSON> kiện Webhook", "webhook.imageGenerated": "<PERSON><PERSON><PERSON> thành tạo hình <PERSON>nh", "webhook.imageGenerationFailed": "<PERSON><PERSON><PERSON> hình <PERSON>nh không thành công", "webhook.creditUpdated": "<PERSON>ố dư tín dụng đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "webhook.payloadFormat": "<PERSON><PERSON><PERSON> dạng tải trọng", "webhook.payloadDescription": "<PERSON><PERSON><PERSON> yêu cầu webhook sẽ được gửi dưới dạng JSON với cấu trúc sau:", "webhook.security": "An ninh", "webhook.securityDescription": "<PERSON><PERSON>g tôi khu<PERSON>ến nghị sử dụng các URL HTTPS và thực hiện xác minh chữ ký để đảm bảo tính xác thực của webhook.", "error.general": "Lỗi", "error.validation": "Lỗi xác nhận", "error.required": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "success.saved": "<PERSON><PERSON><PERSON> thành công", "success.created": "<PERSON><PERSON><PERSON> thành công", "success.deleted": "<PERSON><PERSON><PERSON> thành công", "success.copied": "Đã sao chép vào khay nhớ tạm", "confirmDelete": "<PERSON><PERSON><PERSON>", "confirmDeleteDescription": "Bạn có chắc chắn muốn xóa mục này không? Hành động này không thể hoàn tác.", "historyDeleted": "<PERSON><PERSON><PERSON> m<PERSON><PERSON> lịch sử thành công.", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể x<PERSON>a mục lịch sử", "models.imagen4Fast": "Hình ảnh 4 Nhanh", "models.imagen4Ultra": "Hình ảnh 4 Ultra", "speech.dialogueGeneration.complete": "<PERSON><PERSON><PERSON> thành tạo hội thoại", "speech.dialogueGeneration.failed": "<PERSON><PERSON><PERSON> hội tho<PERSON>i không thành công", "speech.dialogueGeneration.pending": "<PERSON><PERSON> chờ tạo hội tho<PERSON>i", "speech.dialogueGeneration.dialogueGen": "<PERSON><PERSON><PERSON>", "speech.dialogueGeneration.successMessage": "<PERSON><PERSON><PERSON> thoại của bạn đã đ<PERSON><PERSON><PERSON> tạo thành công", "speech.speechGeneration.complete": "<PERSON><PERSON><PERSON> tất <PERSON>", "speech.speechGeneration.failed": "T<PERSON>o giọng nói không thành công", "speech.speechGeneration.pending": "<PERSON><PERSON> chờ tạo bài phát biểu", "speech.speechGeneration.successMessage": "<PERSON><PERSON><PERSON> phát biểu của bạn đã đư<PERSON><PERSON> tạo thành công.", "speech.speechGeneration.requestWaiting": "<PERSON><PERSON><PERSON> cầu tạo bài phát biểu của bạn đang chờ được xử lý.", "speech.errors.failedToLoadEmotions": "<PERSON><PERSON><PERSON><PERSON> tả<PERSON> đ<PERSON><PERSON><PERSON> cảm xúc", "Credits: {credits} remaining": "<PERSON><PERSON> dư còn lại: {credits}", "This generation will cost: {cost} Credits": "<PERSON>ần tạo này sẽ tốn: {cost} Tín dụng", "Your generated video will appear here": "Video được tạo sẽ xuất hiện ở đây", "Regenerate Video": "<PERSON><PERSON>o lại video", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "Bạn chưa thực hiện bất kỳ thay đổi nào đối với cài đặt. Bạn có chắc chắn muốn tái tạo lại cùng một video không?", "Your generated speech will appear here": "<PERSON><PERSON><PERSON> phát biểu bạn tạo ra sẽ xuất hiện ở đây.", "Regenerate Speech": "<PERSON><PERSON><PERSON> tạo g<PERSON> nói", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "Bạn chưa thay đổi bất kỳ cài đặt nào. Bạn có chắc chắn muốn tạo lại cùng một bài phát biểu không?", "Generated Speech": "<PERSON><PERSON><PERSON> phát biểu đ<PERSON><PERSON> tạo ra", "Generating speech...": "<PERSON><PERSON> tạo gi<PERSON> nói...", "View Details": "<PERSON>em chi tiết", "Speech Examples": "<PERSON><PERSON> dụ phát biểu", "Click on any example to use its prompt for speech generation": "<PERSON><PERSON><PERSON><PERSON> và<PERSON> bất kỳ ví dụ nào để sử dụng gợi ý của nó cho việc tạo giọng nói.", "Click to use": "Nhấn để sử dụng", "videoStyles.selectVideoStyle": "<PERSON><PERSON><PERSON>", "videoStyles.cinematic": "<PERSON><PERSON><PERSON><PERSON>", "videoStyles.realistic": "<PERSON><PERSON><PERSON><PERSON> tế", "videoStyles.animated": "<PERSON><PERSON><PERSON>", "videoStyles.artistic": "<PERSON><PERSON><PERSON>", "videoStyles.documentary": "<PERSON><PERSON> tài li<PERSON>u", "videoStyles.vintage": "<PERSON><PERSON> điển", "ui.buttons.downloadApp": "<PERSON><PERSON><PERSON> xu<PERSON>ng <PERSON>ng dụng", "ui.buttons.signUp": "<PERSON><PERSON><PERSON> ký", "ui.buttons.viewDetails": "<PERSON>em chi tiết", "ui.buttons.seeLater": "<PERSON>em sau", "ui.buttons.selectFile": "<PERSON><PERSON><PERSON>", "ui.buttons.selectFiles": "<PERSON><PERSON><PERSON>", "ui.buttons.pickAVoice": "<PERSON><PERSON><PERSON> một gi<PERSON> nói", "ui.buttons.topUpNow": "<PERSON><PERSON><PERSON> tiền ngay bây giờ", "ui.buttons.pressEscToClose": "Nhấn ESC để đóng", "ui.labels.clickToCopy": "<PERSON><PERSON><PERSON><PERSON> để sao chép", "ui.labels.copiedToClipboard": "Đã sao chép vào bảng tạm", "ui.labels.noAudioAvailable": "<PERSON><PERSON><PERSON><PERSON> có âm thanh khả dụng", "ui.labels.noThumbnailAvailable": "<PERSON><PERSON><PERSON>ng có hình thu nhỏ nào có sẵn", "ui.labels.noPromptAvailable": "<PERSON><PERSON><PERSON>ng có hướng dẫn nào có sẵn.", "ui.labels.videoModel": "<PERSON><PERSON>", "ui.labels.speechModel": "<PERSON><PERSON> hình phát âm", "ui.labels.generatedSpeech": "<PERSON><PERSON><PERSON> phát biểu đ<PERSON><PERSON> tạo ra", "ui.labels.defaultVoice": "<PERSON><PERSON><PERSON><PERSON> nói mặc định", "ui.labels.selectAnyVoice": "<PERSON><PERSON><PERSON> b<PERSON>t kỳ giọng nói nào", "ui.labels.cameraMotion": "Chuyển động m<PERSON><PERSON>nh", "ui.labels.transform": "<PERSON><PERSON><PERSON><PERSON> đổi", "ui.labels.transforming": "Chuyển đổi...", "ui.messages.imageLoaded": "<PERSON><PERSON><PERSON>nh đã đ<PERSON><PERSON><PERSON> tải", "ui.messages.imageRevealComplete": "<PERSON><PERSON><PERSON> ảnh đã đư<PERSON><PERSON> tiết lộ hoàn toàn", "ui.messages.processingImage": "<PERSON><PERSON> lý h<PERSON>nh <PERSON>nh", "ui.messages.videoLoaded": "Video đã tải xong", "ui.messages.videoProcessing": "<PERSON><PERSON> lý video", "ui.messages.invalidDownloadLink": "<PERSON><PERSON><PERSON> kết tải xuống không hợp lệ", "ui.messages.pleaseSelectSupportedFile": "<PERSON><PERSON> lòng chọn một tệp được hỗ trợ", "ui.messages.deleteConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "ui.messages.deleteFailed": "<PERSON><PERSON><PERSON> không thành công", "ui.messages.youHaveNewNotification": "Bạn có một thông báo mới.", "ui.messages.yourGenerationIsReady": "<PERSON><PERSON><PERSON> hệ của bạn đã sẵn sàng.", "ui.errors.errorLoadingImage": "Lỗi tải hình ảnh:", "ui.errors.failedToCopy": "<PERSON><PERSON><PERSON>ng thể sao chép: ", "ui.errors.failedToPlayAudioPreview": "<PERSON><PERSON><PERSON><PERSON> thể phát âm thanh xem trước:", "ui.errors.wavesurferError": "Lỗi Wavesurfer:", "ui.errors.somethingWentWrong": "<PERSON><PERSON> xảy ra sự cố. <PERSON><PERSON> lòng thử lại.", "ui.errors.supabaseUrlRequired": "URL Supabase và khóa ẩn danh là bắt buộc", "dialog.startTypingHere": "<PERSON><PERSON><PERSON> đầu nhập hội thoại tại đây...", "payment.debitCreditCard": "Thẻ ghi nợ hoặc thẻ tín dụng", "payment.cardDescription": "Visa, Mastercard, American Express và nhiều hơn nữa", "Style Description": "<PERSON><PERSON> tả phong cách", "Dialog Content": "<PERSON><PERSON><PERSON> dung hộp tho<PERSON>i", "Your generated dialog will appear here": "<PERSON><PERSON><PERSON>n hội thoại được tạo của bạn sẽ xuất hiện ở đây", "Regenerate Dialog": "<PERSON><PERSON><PERSON> tạo hội thoại", "Generated Dialog": "<PERSON><PERSON><PERSON> tho<PERSON><PERSON> đ<PERSON><PERSON> tạo ra", "Generating dialog...": "<PERSON><PERSON><PERSON> hội thoại...", "Dialog Information": "<PERSON>h<PERSON><PERSON> tin đối thoại", "Audio Player": "<PERSON><PERSON><PERSON><PERSON>", "Voices": "Những gi<PERSON> nói", "Voice 1": "Giọng 1", "Voice 2": "Giọng 2", "Dialog Examples": "<PERSON>ác ví dụ hội thoại", "Click on any example to use its style or dialog content": "<PERSON><PERSON><PERSON><PERSON> vào bất kỳ ví dụ nào để sử dụng phong cách hoặc nội dung hội thoại của nó.", "Use Style": "Sử dụng phong cách", "Use Dialog": "<PERSON><PERSON> d<PERSON><PERSON> tho<PERSON>", "personGeneration": "Tạo ra con người", "Imagen": "<PERSON><PERSON><PERSON>", "On": "<PERSON><PERSON><PERSON>", "Off": "Tắt", "Prompts will always be refined to improve output quality": "<PERSON><PERSON><PERSON> lời nhắc sẽ luôn được tinh chỉnh để cải thiện chất lượng đầu ra.", "Prompts will not be modified": "<PERSON><PERSON><PERSON> lời nhắc sẽ không bị thay đổi", "Tips": "Mẹo", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Video của bạn vẫn đang được tạo trong nền. Bạn có thể đóng trang này và kiểm tra tab lịch sử để xem video đã tạo, và chúng tôi sẽ thông báo cho bạn khi nó sẵn sàng.", "Go to History": "<PERSON><PERSON> t<PERSON><PERSON> sử", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Chuyển văn bản thành giọng nói OpenAI", "footer.privacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON>", "footer.termsOfService": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "footer.terms": "<PERSON><PERSON><PERSON><PERSON>", "footer.privacy": "<PERSON><PERSON><PERSON><PERSON> riêng tư", "Generate": "Tạo", "Prompt": "<PERSON><PERSON><PERSON>", "Generate Video": "Tạo video", "ui.errors.generationFailed": "<PERSON><PERSON><PERSON> không thành công", "downloadVideo": "T<PERSON><PERSON> video", "imageStyles.selectImageStyle": "<PERSON><PERSON><PERSON> ki<PERSON>u h<PERSON>nh <PERSON>", "imageStyles.none.description": "<PERSON><PERSON><PERSON><PERSON> áp dụng phong cách cụ thể nào", "imageStyles.3d-render.description": "<PERSON><PERSON><PERSON> xuất hình <PERSON>nh trong 3D", "imageStyles.acrylic.description": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh với phong cách sơn acrylic", "imageStyles.anime-general.description": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh theo phong cách anime", "imageStyles.creative.description": "<PERSON><PERSON> d<PERSON>ng các hi<PERSON>u <PERSON>ng nghệ thuật sáng tạo", "imageStyles.dynamic.description": "<PERSON><PERSON><PERSON> hình ảnh động và năng động", "imageStyles.fashion.description": "<PERSON><PERSON><PERSON>nh phong cách cho nhiếp ảnh thời trang", "imageStyles.game-concept.description": "T<PERSON><PERSON>t kế hình ảnh cho nghệ thuật ý tưởng trò chơi", "imageStyles.graphic-design-3d.description": "<PERSON><PERSON> d<PERSON> các yếu tố thiết kế đồ họa 3D", "imageStyles.illustration.description": "<PERSON><PERSON><PERSON> tác phẩm nghệ thuật theo phong cách minh họa", "imageStyles.portrait.description": "<PERSON><PERSON><PERSON>u hóa cho nhiếp <PERSON>nh chân dung", "imageStyles.portrait-cinematic.description": "<PERSON><PERSON><PERSON> kiểu chân dung điện ảnh", "imageStyles.portrait-fashion.description": "<PERSON><PERSON> dụng phong cách chân dung thời trang", "imageStyles.ray-traced.description": "<PERSON><PERSON><PERSON> xuất với hiệu ứng dò tia ánh sáng", "imageStyles.stock-photo.description": "<PERSON><PERSON><PERSON> ki<PERSON><PERSON>nh chứng kho<PERSON> chuyên nghi<PERSON>p", "imageStyles.watercolor.description": "<PERSON><PERSON> dụng hiệu ứng vẽ màu nước", "imageStyles.examples": "Các ví dụ", "ui.messages.dragDropOrClick": "<PERSON>éo và thả tệp vào đây hoặc nhấp để chọn", "ui.messages.dropFilesHere": "<PERSON><PERSON><PERSON> tệp tin vào đây", "ui.messages.selectMultipleFiles": "<PERSON><PERSON><PERSON> có thể chọn nhiều tệp", "ui.messages.selectSingleFile": "<PERSON><PERSON><PERSON> một tệp để tải lên", "ui.messages.supportedFormats": "<PERSON><PERSON><PERSON> định dạng được hỗ trợ", "ui.messages.releaseToUpload": "<PERSON><PERSON><PERSON> hành để tải lên", "ui.labels.generatedAudio": "<PERSON><PERSON> <PERSON>h đ<PERSON><PERSON><PERSON> tạo ra", "ui.actions.showResult": "<PERSON><PERSON><PERSON> thị kết quả", "ui.actions.hideResult": "Ẩn kết quả", "ui.messages.speechGenerating": "<PERSON><PERSON> tạo gi<PERSON> nói...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "<PERSON><PERSON>i nói của bạn đang được tạo tự động ở chế độ nền. Bạn có thể đóng trang này và kiểm tra thẻ lịch sử để xem âm thanh đã được tạo, chúng tôi sẽ thông báo cho bạn khi nó sẵn sàng.", "downloadAudio": "<PERSON><PERSON><PERSON> xu<PERSON>ng <PERSON>h", "All Countries": "<PERSON><PERSON><PERSON> cả các quốc gia", "All Genders": "<PERSON><PERSON>i gi<PERSON>i t<PERSON>h", "Country": "Quốc gia", "Gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "Reset": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> lại", "Search by name or description...": "<PERSON><PERSON><PERSON> kiếm theo tên hoặc mô tả...", "Male": "Nam", "Female": "<PERSON><PERSON>", "American": "<PERSON><PERSON><PERSON><PERSON>", "British": "<PERSON><PERSON><PERSON><PERSON>", "Australian": "Ngườ<PERSON>", "Indian": "Ấn Độ", "Chinese": "<PERSON><PERSON><PERSON>ng <PERSON>", "Spanish": "Tiếng Tâ<PERSON>", "Canadian": "Người Canada", "Irish": "Tiếng Ireland", "Singaporean": "Người Singapore", "Russian": "<PERSON><PERSON><PERSON><PERSON>", "German": "<PERSON><PERSON><PERSON><PERSON>", "Portuguese": "Tiếng Bồ Đào Nha", "Hindi": "Tiếng Hindi", "Mexican": "Mexico", "Latin American": "<PERSON><PERSON>", "Argentine": "Argentina", "Peninsular": "<PERSON><PERSON>", "French": "<PERSON><PERSON><PERSON><PERSON>", "Parisian": "Người Paris", "Standard": "<PERSON><PERSON><PERSON><PERSON>", "Brazilian": "Brazil", "Turkish": "<PERSON><PERSON><PERSON><PERSON>", "Istanbul": "Istanbul", "Bavarian": "Bavarian", "Polish": "Ba Lan", "Italian": "Tiếng Ý", "South African": "<PERSON><PERSON><PERSON><PERSON>", "Scottish": "Người Scotland", "Welsh": "Tiếng Wales", "New Zealand": "New Zealand", "Dutch": "<PERSON><PERSON>", "Belgian": "Người Bỉ", "Swedish": "<PERSON><PERSON><PERSON><PERSON>", "Norwegian": "Na <PERSON>", "Danish": "<PERSON><PERSON><PERSON><PERSON>", "Korean": "<PERSON><PERSON><PERSON><PERSON>", "Korean, Seoul": "<PERSON><PERSON><PERSON>, Seoul", "Japanese": "<PERSON><PERSON><PERSON><PERSON>", "Croatian": "Tiếng Croatia", "Czech": "Séc", "Moravian": "Moravian", "Zealandic": "Zealandic", "Indonesian": "Người Indonesia", "Javanese": "Tiếng Java", "Romanian": "Tiếng Romania", "Swiss": "<PERSON><PERSON><PERSON><PERSON>", "Vietnamese": "Tiếng <PERSON>", "Arabic": "Tiếng Ả Rập", "Bulgarian": "Tiếng Bulgaria", "Finnish": "<PERSON><PERSON><PERSON>", "Greek": "<PERSON><PERSON>", "Hungarian": "Tiếng Hungary", "Filipino": "Người Philippines", "History": "<PERSON><PERSON><PERSON> s<PERSON>", "imagen-flash": "Gemini 2.0 Flash", "Detail": "<PERSON> ti<PERSON>", "Delete": "Xóa", "ui.errors.unknownError": "<PERSON><PERSON> xảy ra một lỗi không xác định.", "ui.errors.tryAgainLater": "<PERSON><PERSON> lòng thử lại sau.", "More": "<PERSON><PERSON><PERSON><PERSON>", "tts-text": "<PERSON><PERSON>", "tts-multi-speaker": "<PERSON><PERSON>", "tts-history": "<PERSON><PERSON>", "tts-history_1": "<PERSON><PERSON>", "tts-history_2": "<PERSON><PERSON>", "tts-history_3": "<PERSON><PERSON>", "voice-training": "<PERSON><PERSON><PERSON> tạo g<PERSON> nói", "voice-training_1": "<PERSON><PERSON><PERSON> tạo g<PERSON> nói", "voice-training_2": "<PERSON><PERSON><PERSON> tạo g<PERSON> nói", "Start writing or paste your text here or select a file to generate speech...": "Bắt đầu viết hoặc dán văn bản của bạn tại đây hoặc chọn một tệp để tạo giọng nói...", "Selecting a voice...": "<PERSON><PERSON> chọn gi<PERSON>ng nói...", "Voices Library": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>i", "Select a voice for your speaker from the library.": "<PERSON><PERSON><PERSON> một giọng nói cho người nói của bạn từ thư viện.", "Next": "<PERSON><PERSON><PERSON><PERSON> theo", "Back": "Quay lại", "Done": "<PERSON><PERSON><PERSON> t<PERSON>t", "I got it!": "Tôi hiểu rồi!", "historyPages.endOfHistory": "Bạn đã đi đến cuối lịch sử.", "Press ESC to close": "Nhấn ESC để đóng", "Your generated image will appear here": "<PERSON><PERSON>nh ảnh bạn tạo ra sẽ xuất hiện ở đây", "Generate Speech": "Tạo g<PERSON> nói", "Start writing or paste your text here to generate speech...": "Bắt đầu viết hoặc dán văn bản của bạn vào đây để tạo ra giọng đọc...", "Video Gen": "Tạo video", "Generate videos from text prompts and images.": "Tạo video từ lời nhắc văn bản và hình <PERSON>nh.", "Speech Gen": "Tạo g<PERSON> nói", "Convert text and documents to natural speech.": "Chuyển đổi văn bản và tài liệu thành giọng nói tự nhiên.", "Dialogue Gen": "<PERSON><PERSON><PERSON> h<PERSON>i tho<PERSON>i", "Create natural conversations with multiple speakers.": "<PERSON><PERSON><PERSON> trò chuyện tự nhiên với nhiều người nói.", "Veo 2": "Veo 2", "Text to Video": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>n bản thành video", "Image to Video": "<PERSON>y<PERSON>n đổi hình <PERSON>nh thành video", "Up to 8 seconds": "Tối đa 8 giây", "1080p Quality": "<PERSON><PERSON><PERSON> 1080p", "Multiple Styles": "<PERSON><PERSON><PERSON><PERSON>", "Text to Speech": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "Document to Speech": "<PERSON><PERSON><PERSON><PERSON> tài li<PERSON>u", "Multi-Speaker Support": "Hỗ trợ nhiều người nói", "50+ Voices": "50+ <PERSON><PERSON><PERSON><PERSON>", "Multiple Languages": "<PERSON><PERSON><PERSON><PERSON> ngôn ngữ", "Emotion Control": "<PERSON><PERSON><PERSON> so<PERSON> cảm x<PERSON>c", "Multi-Speaker Dialogue": "<PERSON><PERSON><PERSON> thoại đa người nói", "Natural Conversations": "<PERSON><PERSON><PERSON> thoại tự nhiên", "Voice Customization": "T<PERSON>y chỉnh Giọng nói", "Emotion Expression": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>t cảm x<PERSON>c", "Script Generation": "<PERSON><PERSON><PERSON> b<PERSON>n", "Audio Export": "<PERSON><PERSON><PERSON>h", "Home": "Trang chủ", "Price per 1 character: {cost} Credits": "Giá mỗi ký tự: {cost} Tín dụng", "veo-2": "Veo 2", "veo-3": "Veo 3", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "Quá trình tạo bài phát biểu của bạn vẫn đang được thực hiện. Bạn có thể đóng trang này và kiểm tra tab lịch sử để xem bài phát biểu đã đượ<PERSON> tạo, chúng tôi sẽ thông báo cho bạn khi nó sẵn sàng.", "Create Another": "Tạo c<PERSON>i k<PERSON>c", "estimated_credit": "Ước tính tín dụng", "tts-flash": "Gemini 2.5 Flash", "Select Another Voice": "Chọn G<PERSON> Kh<PERSON>", "Custom prompt {count}": "T<PERSON><PERSON> chỉnh prompt {count}", "Custom prompt": "<PERSON><PERSON><PERSON> nhắc tùy chỉnh", "Prompt name": "<PERSON><PERSON><PERSON> prompt", "This name will help you identify your prompt.": "<PERSON>ên này sẽ gi<PERSON>p bạn nhận diện lời nhắc của mình.", "Save as new": "<PERSON><PERSON><PERSON> d<PERSON>i dạng mới", "Ok, save it!": "<PERSON><PERSON><PERSON><PERSON>, lưu nó lại!", "Don't use": "Không sử dụng", "Use": "Sử dụng", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "Bạn có quyền sử dụng đầu ra giọng nói do dịch vụ của chúng tôi tạo ra cho mục đích cá nhân, gi<PERSON><PERSON> dục hoặc thương mại.", "Your saved prompts": "<PERSON><PERSON><PERSON> lời nhắc đã lưu của bạn", "Success": "<PERSON><PERSON><PERSON><PERSON> công", "Saved prompt successfully": "<PERSON><PERSON><PERSON> l<PERSON> n<PERSON>c thành công", "Error": "Lỗi", "Saved prompt failed": "<PERSON><PERSON><PERSON> l<PERSON> n<PERSON>c không thành công", "Updated prompt successfully": "<PERSON><PERSON><PERSON><PERSON> prompt thà<PERSON> công", "Updated prompt failed": "<PERSON><PERSON><PERSON> nhật lời nhắc thất bại", "Are you sure you want to delete this prompt?": "Bạn có chắc chắn muốn xóa lời nhắc này không?", "Deleted prompt successfully": "<PERSON><PERSON><PERSON> lời nh<PERSON>c thành công", "Deleted prompt failed": "<PERSON><PERSON><PERSON> cầu đã xoá không thành công", "Enter your custom prompt here.": "<PERSON><PERSON><PERSON><PERSON> lời nhắc tùy chỉnh của bạn ở đây.", "Ex: Funny prompt": "Ví dụ: <PERSON><PERSON><PERSON> ý hài hước", "Discard": "Bỏ đi", "Update": "<PERSON><PERSON><PERSON>", "Edit": "Chỉnh sửa", "Custom Prompt": "T<PERSON><PERSON> chỉnh prompt", "Your credits will never expire.": "<PERSON>ín dụng của bạn sẽ không bao giờ hết hạn.", "Available credits": "Tín dụng khả dụng", "{n}+ Styles": "{n}+ <PERSON><PERSON><PERSON> dáng", "Create images from text prompts.": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh từ lời nhắc văn bản.", "/Image": "/<PERSON><PERSON>nh <PERSON>nh", "/Video": "/Video", "/1 character": "/1 ký tự", "Buy credits": "<PERSON><PERSON>", "My Account": "<PERSON><PERSON><PERSON>n của tôi", "Manage your account, credits, and orders.": "<PERSON><PERSON><PERSON><PERSON> lý tà<PERSON>, tín dụng và đơn hàng của bạn.", "Full Name": "Họ và Tên", "Total Available Credits": "Tổng số tín chỉ khả dụng", "Locked Credits": "<PERSON>ín chỉ bị khóa", "Save changes": "<PERSON><PERSON><PERSON> thay đổi", "Your account has been updated.": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t.", "This field is required.": "<PERSON>rư<PERSON><PERSON> này là b<PERSON> bu<PERSON>.", "User Info": "Thông tin người dùng", "Email": "<PERSON><PERSON><PERSON> đi<PERSON>n tử", "Used to sign in, for email receipts and product updates.": "<PERSON><PERSON><PERSON><PERSON> sử dụng để đăng nhập, nhận biên lai email và cập nhật sản phẩm.", "Active and valid credits only": "Chỉ các khoản tín dụng đang hoạt động và còn hiệu lực.", "We lock your credits to perform transactions.": "<PERSON><PERSON>g tôi khóa tín dụng của bạn để thực hiện các giao dịch.", "Referral Link": "<PERSON><PERSON><PERSON> kết gi<PERSON>i thiệu", "Share your referral link to earn credits.": "<PERSON>a sẻ liên kết giới thiệu của bạn để kiếm tín dụng.", "Referral Code": "<PERSON>ã giới thiệu", "Your Referral Code": "Mã Giới Thiệu Của Bạn", "Copy": "Sao chép", "Copied!": "Đã sao chép!", "Orders": "<PERSON><PERSON><PERSON> hàng", "Manage your orders.": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng của bạn.", "Will appear on receipts, invoices, and other communication.": "Sẽ xuất hiện trên biên la<PERSON>, hóa đơn và các giao tiếp kh<PERSON>c.", "User Information": "Thông tin người dùng", "Must be at least 8 characters": "Phải có ít nhất 8 ký tự", "Passwords must be different": "<PERSON><PERSON><PERSON> kh<PERSON>u ph<PERSON>i kh<PERSON>c nhau.", "Passwords must match": "<PERSON><PERSON><PERSON> kh<PERSON>u ph<PERSON>i khớp nhau", "Current password": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "New password": "<PERSON><PERSON><PERSON> mới", "Confirm new password": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "Password": "<PERSON><PERSON><PERSON>", "Confirm your current password before setting a new one.": "<PERSON><PERSON><PERSON> nhận mật khẩu hiện tại của bạn trước khi đặt mật khẩu mới.", "Account": "<PERSON><PERSON><PERSON>", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "Không còn muốn sử dụng dịch vụ của chúng tôi? Bạn có thể xóa tài khoản của mình tại đây. Hành động này không thể đảo ngược. Tất cả thông tin liên quan đến tài khoản này sẽ bị xóa vĩnh viễn.", "Delete account": "<PERSON><PERSON><PERSON> t<PERSON>", "Change Password": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "Security": "<PERSON><PERSON><PERSON>", "Credit Statistics": "<PERSON><PERSON><PERSON><PERSON> kê Tín dụng", "enhance_prompt": "<PERSON><PERSON><PERSON> cao lời n<PERSON>c", "Current Plan": "<PERSON><PERSON> hoạch hiện tại", "When you buy credits, you will be upgraded to Premium Plan.": "<PERSON><PERSON> bạn mua tín dụng, bạn sẽ được nâng cấp lên gói Premium.", "Available Credits": "Tín dụng khả dụng", "Purchased Credits": "Đ<PERSON> mua Tín dụng", "Plan Credits": "<PERSON>ín dụng được tặng", "profile.passwordChanged": "<PERSON><PERSON><PERSON> khẩu đã đư<PERSON>c thay đổi", "profile.passwordChangedDescription": "<PERSON><PERSON><PERSON> kh<PERSON>u của bạn đã đư<PERSON><PERSON> thay đổi thành công.", "profile.passwordChangeError": "<PERSON><PERSON><PERSON> mật khẩu không thành công", "profile.passwordChangeErrorDescription": "<PERSON><PERSON> lỗi xảy ra khi thay đổi mật khẩu của bạn. <PERSON><PERSON> lòng thử lại.", "delete": "Xóa", "profile.deleteAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "profile.deleteAccountConfirmation": "Bạn có chắc chắn muốn xóa tài khoản của mình không? Hành động này không thể hoàn tác và tất cả dữ liệu của bạn sẽ bị mất vĩnh viễn.", "profile.accountDeleted": "<PERSON><PERSON><PERSON> k<PERSON>n đã bị xóa", "profile.accountDeletedDescription": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đã đư<PERSON>c xóa thành công.", "profile.accountDeletionError": "<PERSON><PERSON><PERSON> Bạ<PERSON>", "profile.accountDeletionErrorDescription": "<PERSON><PERSON> xảy ra lỗi khi xóa tài khoản của bạn. <PERSON><PERSON> lòng thử lại.", "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.": "<PERSON><PERSON><PERSON> dịp ra mắ<PERSON>, chúng tôi mang đến chương trình giảm giá 50% cho một số Model  dịch vụ API Gemini. Chương trình khuyến mãi áp dụng cho đến khi có thông báo mới.", "Check now": "<PERSON><PERSON><PERSON> tra ngay", "payment.success.title": "<PERSON>h toán thành công!", "payment.success.message": "Cảm ơn bạn đã mua hàng! <PERSON>h toán của bạn đã được xử lý thành công.", "payment.success.orderId": "<PERSON><PERSON> đơn hàng:", "payment.success.redirecting": "<PERSON><PERSON> chuyển hướng đến đơn hàng của bạn trong {seconds} giây...", "payment.success.viewOrders": "<PERSON><PERSON> đ<PERSON>n hàng của tôi", "payment.error.title": "Lỗi thanh toán", "payment.error.message": "<PERSON><PERSON> xảy ra lỗi khi xử lý thanh toán của bạn. <PERSON><PERSON> lòng liên hệ hỗ trợ nếu tình trạng này tiếp tục.", "payment.error.backToOrders": "Quay lại đơn hàng", "Overview of your credits status.": "Tổng quan về trạng thái tín dụng của bạn.", "Payment History": "<PERSON><PERSON><PERSON> sử thanh toán", "Your payment history will appear here once you have made a purchase.": "<PERSON><PERSON><PERSON> sử thanh toán của bạn sẽ xuất hiện ở đây sau khi bạn đã thực hiện một giao dịch mua.", "Payment method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "Purchase Date": "<PERSON><PERSON><PERSON> mua hàng", "Amount": "Số lượng", "Status": "<PERSON><PERSON><PERSON><PERSON> thái", "Payment amount": "<PERSON><PERSON> tiền thanh toán", "payment.status.unavailable": "<PERSON><PERSON><PERSON><PERSON> khả dụng", "payment.status.created": "<PERSON><PERSON><PERSON><PERSON> tạo ra", "payment.status.completed": "<PERSON><PERSON> hoàn thành", "payment.status.failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "payment.status.canceled": "<PERSON><PERSON> hủy", "payment.status.processing": "<PERSON><PERSON> lý", "payment.status.refund": "<PERSON><PERSON><PERSON> ti<PERSON>n", "payment.status.partial_paid": "<PERSON><PERSON> <PERSON><PERSON> một ph<PERSON>n", "apiKeys.successTitle": "Tạo khóa API thành công", "apiKeys.importantNotice": "<PERSON><PERSON><PERSON><PERSON> báo quan trọng", "apiKeys.copyWarning": "Đây là lần duy nhất bạn có thể xem và sao chép khóa API này. Vui lòng sao chép ngay bây giờ và lưu trữ nó một cách an toàn.", "apiKeys.key": "Mã API", "apiKeys.securityTip": "Mẹo <PERSON><PERSON><PERSON>:", "apiKeys.tip1": "<PERSON><PERSON><PERSON> k<PERSON> này ở một nơi an toàn", "apiKeys.tip2": "<PERSON>h<PERSON>ng bao giờ chia sẻ khóa API của bạn công khai", "apiKeys.tip3": "\"<PERSON><PERSON><PERSON> bị xâm phạm, hãy xóa khóa này và tạo một cái mới\"", "apiKeys.copyFirst": "Sao chép khóa API trước tiên", "common.done": "<PERSON><PERSON><PERSON> th<PERSON>", "Integration": "<PERSON><PERSON><PERSON>", "API Keys": "Khóa API", "Manage your API keys.": "Quản lý khóa API của bạn.", "Create API Key": "Tạo khóa API", "Name your API key.": "Đặt tên cho khóa API của bạn.", "Create": "Tạo ra", "You have not created any API keys yet.": "Bạn chưa tạo bất kỳ khóa API nào.", "Copy API Key": "Sao chép khóa API", "Delete API Key": "Xóa khóa API", "EMAIL_NOT_EXIST": "<PERSON><PERSON> không tồn tại", "Your account is not verified": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn chưa đ<PERSON><PERSON><PERSON> xác <PERSON>h", "Your account is not verified. Please verify your account to continue": "<PERSON><PERSON><PERSON> khoản của bạn chưa được xác minh. <PERSON><PERSON> lòng xác minh tài khoản của bạn để tiếp tục.", "TOKEN_USED": "<PERSON><PERSON> thông báo đã được sử dụng.", "NOT_ENOUGH_CREDIT": "<PERSON><PERSON><PERSON>ng đủ số dư. <PERSON><PERSON> lòng nạp tiền vào tài khoản của bạn.", "Not enough credit": "Không đủ tín dụng", "Your account does not have enough credit. Please top up your account to continue.": "<PERSON>à<PERSON> khoản của bạn không đủ tiền. <PERSON><PERSON> lòng nạp thêm tiền vào tài khoản để tiếp tục.", "{n} credits": "{n} tín chỉ", "USD / {unit}": "USD / {unit}", "Credits / {unit}": "Tín chỉ / {unit}", "Save {n}%": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> {n}%", "${price} = {n} credits": "${price} = {n} tín dụng", "You can switch between money and credits to see the price in your preferred currency.": "Bạn có thể chuyển đổi giữa tiền và tín dụng để xem giá bằng đơn vị tiền tệ ưa thích của mình.", "Forever": "<PERSON><PERSON><PERSON> mãi", "For large organizations.": "<PERSON><PERSON><PERSON> cho các tổ chức lớn.", "Free": "<PERSON><PERSON><PERSON> phí", "Contact us": "<PERSON><PERSON><PERSON>", "Contact sales": "<PERSON><PERSON><PERSON> hệ nhân viên", "Premium": "<PERSON> cấp", "Enterprise": "<PERSON><PERSON><PERSON>", "Show money": "<PERSON><PERSON><PERSON> thị tiền", "Show credits": "<PERSON><PERSON><PERSON> thị tín dụng", "Auto upgrade after buy credits": "Tự động nâng cấp sau khi mua tín dụng", "Image": "Ảnh", "Video": "Video", "Audio": "<PERSON><PERSON>", "Dialog": "<PERSON><PERSON><PERSON> tho<PERSON>", "Get started": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Contact": "<PERSON><PERSON><PERSON>", "Image Style": "<PERSON><PERSON> c<PERSON>ch h<PERSON>nh <PERSON>", "Image Aspect Ratio": "Tỷ lệ khung hình của hình ảnh", "Enhance Prompt": "<PERSON><PERSON>ng cường gợi ý", "Aspect Ratio": "Tỷ l<PERSON> khung hình", "Support multiple aspect ratio": "Hỗ trợ nhiều tỷ lệ khung hình", "Support enhance prompt": "Hỗ trợ nâng cao lời nhắc", "Up to {size}MB": "<PERSON><PERSON><PERSON> đ<PERSON>n {size}MB"}