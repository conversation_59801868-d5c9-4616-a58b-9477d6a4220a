<script setup lang="ts">
const { t } = useI18n()
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)
const creditsStore = useCreditsStore()

const showMode = ref('money') // money | credits

// Pricing Calculator state
const calculatorMode = ref('budget') // budget | resources
const budgetAmount = ref(10) // minimum $10
const resourceCounts = ref<Record<string, number>>({
  'imagen-flash': 0,
  'imagen-4-fast': 0,
  'imagen-4': 0,
  'imagen-4-ultra': 0,
  'veo-2': 0,
  'veo-3-fast': 0,
  'veo-3': 0
})
const showModeItems = computed(() => {
  return [
    {
      label: t('Show money'),
      value: 'money',
      icon: 'solar:dollar-bold'
    },
    {
      label: t('Show credits'),
      value: 'credits',
      icon: 'akar-icons:coin'
    }
  ]
})

// Calculator mode items
const calculatorModeItems = computed(() => {
  return [
    {
      label: t('Budget Calculator'),
      value: 'budget',
      icon: 'solar:dollar-bold'
    },
    {
      label: t('Resource Calculator'),
      value: 'resources',
      icon: 'solar:gallery-bold'
    }
  ]
})

// Service pricing data
const servicePricing = computed(() => {
  return [
    {
      id: 'imagen-flash',
      name: t('Gemini 2.0 Flash'),
      type: 'image',
      icon: 'i-lucide-zap',
      iconColor: 'text-yellow-500',
      priceUnit: 0.039,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-flash')?.effective_price || 20
    },
    {
      id: 'imagen-4-fast',
      name: t('Imagen 4 Fast'),
      type: 'image',
      icon: 'i-lucide-image',
      iconColor: 'text-blue-500',
      priceUnit: 0.02,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-4-fast')?.effective_price
        || 10
    },
    {
      id: 'imagen-4',
      name: t('Imagen 4'),
      type: 'image',
      icon: 'i-lucide-image-plus',
      iconColor: 'text-green-500',
      priceUnit: 0.04,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-4')?.effective_price || 20
    },
    {
      id: 'imagen-4-ultra',
      name: t('Imagen 4 Ultra'),
      type: 'image',
      icon: 'i-lucide-crown',
      iconColor: 'text-purple-500',
      priceUnit: 0.06,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('imagen-4-ultra')?.effective_price
        || 30
    },
    {
      id: 'veo-2',
      name: t('Veo 2'),
      type: 'video',
      icon: 'i-lucide-video',
      iconColor: 'text-red-500',
      priceUnit: 0.5,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('veo-2')?.effective_price || 250
    },
    {
      id: 'veo-3-fast',
      name: t('Veo 3 Fast'),
      type: 'video',
      icon: 'i-lucide-play-circle',
      iconColor: 'text-orange-500',
      priceUnit: 0.5,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('veo-3-fast')?.effective_price || 250
    },
    {
      id: 'veo-3',
      name: t('Veo 3'),
      type: 'video',
      icon: 'i-lucide-film',
      iconColor: 'text-indigo-500',
      priceUnit: 0.75,
      discount: 0.5,
      credits:
        getServicePriceByModelName.value('veo-3')?.effective_price || 375
    }
  ]
})

// Calculate resources from budget
const calculatedResources = computed(() => {
  const budget = budgetAmount.value
  const creditRate = 1000 // $1 = 1000 credits
  const totalCredits = budget * creditRate

  return servicePricing.value.map((service) => {
    const maxResources = Math.floor(totalCredits / service.credits)
    return {
      ...service,
      maxResources,
      actualPrice: service.priceUnit * service.discount
    }
  })
})

// Calculate total price from resources
const calculatedPrice = computed(() => {
  let totalCredits = 0

  servicePricing.value.forEach((service) => {
    const count = resourceCounts.value[service.id] || 0
    totalCredits += count * service.credits
  })

  const creditRate = 1000 // $1 = 1000 credits
  return totalCredits / creditRate
})

// Check if purchase amount meets minimum requirement
const canPurchase = computed(() => {
  if (calculatorMode.value === 'budget') {
    return budgetAmount.value >= 10
  } else {
    return calculatedPrice.value >= 10
  }
})

// Get the actual purchase amount
const purchaseAmount = computed(() => {
  if (calculatorMode.value === 'budget') {
    return budgetAmount.value
  } else {
    return calculatedPrice.value
  }
})

// Functions
const handleBuyNow = () => {
  let creditsNeeded = 0
  let totalPrice = 0

  if (calculatorMode.value === 'budget') {
    totalPrice = budgetAmount.value
    creditsNeeded = budgetAmount.value * 1000 // $1 = 1000 credits
  } else {
    totalPrice = calculatedPrice.value
    servicePricing.value.forEach((service) => {
      const count = resourceCounts.value[service.id] || 0
      creditsNeeded += count * service.credits
    })
  }

  // Check minimum purchase amount
  if (totalPrice < 10) {
    const toast = useToast()
    toast.add({
      id: 'minimum-purchase-error',
      title: t('Minimum Purchase Required'),
      description: t(
        'Minimum purchase amount is $10. Please increase your selection.'
      ),
      color: 'error'
    })
    return
  }

  if (creditsNeeded > 0) {
    creditsStore.processBuyCredits(creditsNeeded)
  }
}

const resetResourceCounts = () => {
  Object.keys(resourceCounts.value).forEach((key) => {
    resourceCounts.value[key] = 0
  })
}

// Get quick values for sliders
const getQuickValues = (type: string) => {
  if (type === 'image') {
    return [100, 1000, 10000, 50000, 100000]
  } else {
    return [50, 500, 5000, 25000, 50000]
  }
}

const tiers = computed(() => {
  return [
    {
      id: 'free',
      title: t('Free'),
      price: '$0',
      description: t('Forever'),
      button: {
        label: t('Get started'),
        variant: 'soft' as const,
        color: 'neutral',
        onclick: () => navigateTo('/auth/signup')
      }
    },
    {
      id: 'premium',
      title: t('Premium'),
      description: t('Auto upgrade after buy credits'),
      price: t('Pay as you go'),
      button: {
        label: t('Buy credits'),
        variant: 'soft' as const,
        color: 'primary',
        onclick: () => navigateTo('/profile/credits')
      },
      highlight: true
    },
    {
      id: 'enterprise',
      title: t('Enterprise'),
      price: t('Contact us'),
      description: t('For large organizations.'),
      button: {
        label: t('Contact sales'),
        variant: 'soft' as const,
        color: 'neutral'
      }
    }
  ]
})

const sections = computed(() => {
  return [
    {
      id: 'imagen',
      title: t('Imagen'),
      features: [
        {
          id: 'imagen-flash',
          title: t('Gemini 2.0 Flash'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.039, // $0.039/image
              discount: 0.5, // 50% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-flash')
                  ?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'imagen-4-fast',
          title: t('Imagen 4 Fast'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.02, // $0.02/image
              discount: 0.5, // 50% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4-fast')
                  ?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'imagen-4',
          title: t('Imagen 4  '),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.04, // $0.04/image
              discount: 0.5, // 50% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'imagen-4-ultra',
          title: t('Imagen 4 Ultra'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.06, // $0.06/image
              discount: 0.5, // 50% discount
              unit: 'Image',
              credits:
                getServicePriceByModelName.value('imagen-4-ultra')
                  ?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'image-style',
          title: t('Image Style'),
          tiers: {
            free: false,
            premium: t('{n}+ Styles', { n: 15 }),
            enterprise: t('{n}+ Styles', { n: 15 })
          }
        },
        // Aspect Ratio
        {
          id: 'image-dimensions',
          title: t('Image Aspect Ratio'),
          tiers: {
            free: false,
            premium: t('Support multiple aspect ratio'),
            enterprise: t('Support multiple aspect ratio')
          }
        },
        {
          id: 'image-reference',
          title: t('Image Reference'),
          tiers: {
            free: false,
            premium: t('Up to {size}MB', { size: 10 }),
            enterprise: t('Up to {size}MB', { size: 10 })
          }
        }
      ]
    },
    {
      id: 'video-gen',
      title: t('Video Gen'),
      features: [
        {
          id: 'veo-2',
          title: t('Veo 2'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.5, // $0.5/video
              discount: 0.5, // 50% discount
              unit: 'Video',
              credits:
                getServicePriceByModelName.value('veo-2')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'veo-3-fast',
          title: t('Veo 3 Fast'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.5, // $0.5/video
              discount: 0.5, // 50% discount
              unit: 'Video',
              credits:
                getServicePriceByModelName.value('veo-3-fast')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'veo-3',
          title: t('Veo 3'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.75, // $0.75/video
              discount: 0.5, // 50% discount
              unit: 'Video',
              credits:
                getServicePriceByModelName.value('veo-3')?.effective_price
            },
            enterprise: t('Contact')
          }
        },
        {
          id: 'videogen-image-reference',
          title: t('Image Reference'),
          tiers: {
            free: false,
            premium: t('Up to {size}MB', { size: 10 }),
            enterprise: t('Up to {size}MB', { size: 10 })
          }
        },
        // Enhance Prompt
        {
          id: 'videogen-enhance-prompt',
          title: t('Enhance Prompt'),
          tiers: {
            free: false,
            premium: t('Support enhance prompt'),
            enterprise: t('Support enhance prompt')
          }
        },
        // Aspect Ratio
        {
          id: 'videogen-aspect-ratio',
          title: t('Aspect Ratio'),
          tiers: {
            free: false,
            premium: t('Support multiple aspect ratio'),
            enterprise: t('Support multiple aspect ratio')
          }
        }
      ]
    },
    {
      id: 'speech-gen',
      title: t('Speech Gen'),
      features: [
        {
          id: 'gemini-2.5-pro',
          title: t('Gemini 2.5 Pro'),
          tiers: {
            free: true,
            premium: '$20/1M characters',
            enterprise: t('Contact')
          }
        },
        {
          id: 'gemini-2.5-flash',
          title: t('Gemini 2.5 Flash'),
          tiers: {
            free: true,
            premium: '$40/1M characters',
            enterprise: t('Contact')
          }
        }
      ]
    }
  ]
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('pricing.title')"
        :description="$t('Simple and flexible. Only pay for what you use.')"
      >
        <template #links />
      </UPageHeader>
    </UContainer>

    <UPageSection
      :ui="{
        container: '!pt-10'
      }"
    >
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div
          class="lg:col-span-3 lg:col-start-2 flex flex-col justify-center items-center gap-2"
        >
          <div class="flex flex-col justify-center items-center gap-0">
            <h2 class="text-xl font-bold">
              {{
                $t("${price} = {n} credits", {
                  price: 1,
                  n: formatNumber(1000)
                })
              }}
            </h2>
            <div class="text-xs text-muted">
              {{
                $t(
                  "You can switch between money and credits to see the price in your preferred currency."
                )
              }}
            </div>
          </div>
          <UTabs
            v-model="showMode"
            color="neutral"
            :content="false"
            :items="showModeItems"
            class="w-fit"
            size="xs"
          />
        </div>
      </div>
      <UPricingTable
        :tiers="tiers"
        :sections="sections"
        class="bg-muted dark:bg-transparent rounded-2xl pl-4 pb-10"
      >
        <!-- Customize specific tier title -->
        <template #premium-title="{ tier }">
          <div class="flex items-center gap-2">
            <UIcon
              name="i-lucide-crown"
              class="size-4 text-amber-500"
            />
            {{ tier.title }}
          </div>
        </template>

        <!-- Customize specific section title -->
        <template #section-security-title="{ section }">
          <div class="flex items-center gap-2">
            <UIcon
              name="i-lucide-shield-check"
              class="size-4 text-green-500"
            />
            <span class="font-semibold text-green-700">{{
              section.title
            }}</span>
          </div>
        </template>

        <!-- Customize specific feature value -->
        <template #feature-value="{ feature, tier }">
          <template v-if="feature.tiers?.[tier.id] === true">
            <UIcon
              name="prime:check-circle"
              class="size-5 text-primary"
            />
          </template>
          <template v-else-if="feature.tiers?.[tier.id]?.priceUnit">
            <div
              class="group flex flex-row items-center relative w-full justify-between gap-1"
            >
              <div class="flex flex-row items-end">
                <div>
                  <div
                    class="text-3xl font-semibold scale-x-90 scale-y-110 group-hover:text-primary"
                  >
                    <span v-if="showMode === 'money'">
                      ${{
                        feature.tiers[tier.id].priceUnit
                          * (feature.tiers[tier.id].discount || 1)
                      }}
                    </span>
                    <span v-else>
                      {{ feature.tiers[tier.id].credits }}
                    </span>
                  </div>
                </div>
                <div class="text-left">
                  <div class="flex items-center gap-1">
                    <span class="relative inline-block">
                      <span class="px-0.5 relative z-10 text-muted opacity-70">
                        {{
                          showMode === "money"
                            ? "$" + feature.tiers[tier.id].priceUnit
                            : t("{n} credits", {
                              n:
                                feature.tiers[tier.id].credits
                                * feature.tiers[tier.id].discount
                            })
                        }}
                      </span>
                      <span
                        class="absolute top-1/2 left-0 right-0 border-t border-gray- dark:border-gray-500 z-0"
                      />
                    </span>
                  </div>
                  <div class="text-xs font-semibold group-hover:text-primary">
                    {{
                      showMode === "money"
                        ? t("USD / {unit}", {
                          unit: t(feature.tiers[tier.id].unit || "")
                        })
                        : t("Credits / {unit}", {
                          unit: t(feature.tiers[tier.id].unit || "")
                        })
                    }}
                  </div>
                </div>
              </div>
              <UBadge
                color="success"
                size="sm"
                variant="soft"
                class="transition-all duration-200 group-hover:scale-150"
              >
                {{
                  feature.tiers[tier.id].discount
                    ? t("Save {n}%", {
                      n: Math.round(
                        (1 - feature.tiers[tier.id].discount) * 100
                      )
                    })
                    : ""
                }}
              </UBadge>
            </div>
          </template>
          <div
            v-else-if="feature.tiers?.[tier.id]"
            class="text-left w-full"
          >
            {{ feature.tiers[tier.id] }}
          </div>
          <UIcon
            v-else
            name="i-lucide-x"
            class="size-5 text-muted"
          />
        </template>
      </UPricingTable>

      <UPageHeader
        :title="$t('Pricing Calculator')"
        :description="
          $t('Calculate how many resources can you generate with your budget.')
        "
      >
        <template #links />
      </UPageHeader>

      <UPricingPlan
        :price="
          calculatorMode === 'budget'
            ? `$${budgetAmount}`
            : `$${calculatedPrice.toFixed(2)}`
        "
        :button="{
          label: canPurchase ? $t('Buy now') : $t('Minimum $10 required'),
          onClick: handleBuyNow,
          disabled: !canPurchase,
          color: canPurchase ? 'primary' : 'neutral'
        }"
        orientation="horizontal"
        variant="outline"
      >
        <template #body>
          <div class="space-y-6 w-full flex-1">
            <!-- Calculator Mode Toggle -->
            <div class="flex justify-center">
              <UTabs
                v-model="calculatorMode"
                color="primary"
                :content="false"
                :items="calculatorModeItems"
                class="w-fit"
                size="sm"
              />
            </div>

            <!-- Budget Calculator Mode -->
            <div
              v-if="calculatorMode === 'budget'"
              class="space-y-4"
            >
              <UFormField
                :label="$t('Budget Amount')"
                class="w-full"
              >
                <div class="space-y-3">
                  <!-- Budget Input Field -->
                  <div class="flex items-center gap-3">
                    <div class="flex-1">
                      <UInputNumber
                        v-model="budgetAmount"
                        :min="10"
                        :max="100000"
                        :step="1"
                        size="lg"
                        class="w-full"
                        :placeholder="$t('Enter budget amount')"
                      >
                        <template #leading>
                          <span class="text-gray-500">$</span>
                        </template>
                      </UInputNumber>
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ $t('Min: $10') }}
                    </div>
                  </div>

                  <!-- Slider -->
                  <USlider
                    v-model="budgetAmount"
                    :min="10"
                    :max="10000"
                    :step="5"
                    class="w-full"
                  />

                  <!-- Quick Budget Values -->
                  <div class="flex justify-between items-center text-sm text-gray-500">
                    <span>$10</span>
                    <div class="flex gap-2">
                      <button
                        v-for="quickBudget in [50, 100, 500, 1000, 5000]"
                        :key="quickBudget"
                        class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        @click="budgetAmount = quickBudget"
                      >
                        ${{ formatNumber(quickBudget) }}
                      </button>
                    </div>
                    <span>$10,000</span>
                  </div>
                </div>
              </UFormField>

              <!-- Resources Display -->
              <div class="space-y-3">
                <h4 class="font-semibold text-sm">
                  {{ $t("Resources you can generate:") }}
                </h4>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div
                    v-for="resource in calculatedResources"
                    :key="resource.id"
                    class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3"
                  >
                    <div class="flex justify-between items-center">
                      <div class="flex items-center gap-3">
                        <div class="flex-shrink-0">
                          <UIcon
                            :name="resource.icon"
                            :class="['size-5', resource.iconColor]"
                          />
                        </div>
                        <div>
                          <div class="font-medium text-sm">
                            {{ resource.name }}
                          </div>
                          <div class="text-xs text-gray-500">
                            {{
                              resource.type === "image"
                                ? $t("Images")
                                : $t("Videos")
                            }}
                          </div>
                        </div>
                      </div>
                      <div class="text-right">
                        <div class="font-bold text-primary">
                          {{ formatNumber(resource.maxResources) }}
                        </div>
                        <div class="text-xs text-gray-500">
                          ${{ resource.actualPrice }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Resource Calculator Mode -->
            <div
              v-else
              class="space-y-4"
            >
              <div class="flex justify-between items-center">
                <h4 class="font-semibold text-sm">
                  {{ $t("Select resources you want:") }}
                </h4>
                <UButton
                  size="xs"
                  variant="ghost"
                  color="neutral"
                  @click="resetResourceCounts"
                >
                  {{ $t("Reset") }}
                </UButton>
              </div>

              <div class="space-y-4">
                <div
                  v-for="service in servicePricing"
                  :key="service.id"
                  class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4"
                >
                  <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center gap-3">
                      <div class="flex-shrink-0">
                        <UIcon
                          :name="service.icon"
                          :class="['size-5', service.iconColor]"
                        />
                      </div>
                      <div>
                        <div class="font-medium text-sm">
                          {{ service.name }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{ service.credits }} {{ $t("credits") }} /
                          {{
                            service.type === "image" ? $t("image") : $t("video")
                          }}
                        </div>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="font-bold text-primary">
                        ${{ (service.priceUnit * service.discount).toFixed(3) }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ $t("per item") }}
                      </div>
                    </div>
                  </div>

                  <UFormField
                    :label="`${$t('Quantity')}: ${resourceCounts[service.id]}`"
                    class="w-full"
                  >
                    <div class="space-y-3">
                      <!-- Slider -->
                      <USlider
                        v-model="resourceCounts[service.id]"
                        :min="0"
                        :max="1000000"
                        :step="1"
                        class="w-full"
                      />

                      <!-- Quick values -->
                      <div class="flex justify-between text-xs text-gray-500">
                        <span>0</span>
                        <div class="flex gap-2">
                          <button
                            v-for="quickValue in getQuickValues(service.type)"
                            :key="quickValue"
                            class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                            @click="resourceCounts[service.id] = quickValue"
                          >
                            {{ formatNumber(quickValue) }}
                          </button>
                        </div>
                        <span>{{ formatNumber(1000000) }}</span>
                      </div>

                      <!-- Input Number for precise control -->
                      <UInputNumber
                        v-model="resourceCounts[service.id]"
                        :min="0"
                        :max="1000000"
                        :step="1"
                        size="sm"
                        class="w-full"
                        :placeholder="$t('Enter exact number')"
                      />
                    </div>
                  </UFormField>
                </div>
              </div>

              <!-- Total Display -->
              <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                <div class="flex justify-between items-center">
                  <span class="font-semibold">{{ $t("Total Cost:") }}</span>
                  <span class="text-xl font-bold text-primary">${{ calculatedPrice.toFixed(2) }}</span>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {{
                    $t("Approximately {credits} credits", {
                      credits: Math.round(calculatedPrice * 1000)
                    })
                  }}
                </div>

                <!-- Minimum Purchase Warning -->
                <div
                  v-if="!canPurchase && calculatedPrice > 0"
                  class="mt-3 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg"
                >
                  <div class="flex items-center gap-2">
                    <UIcon
                      name="i-lucide-alert-triangle"
                      class="size-4 text-amber-600 dark:text-amber-400"
                    />
                    <span
                      class="text-sm text-amber-800 dark:text-amber-200 font-medium"
                    >
                      {{ $t("Minimum purchase amount is $10") }}
                    </span>
                  </div>
                  <div class="text-xs text-amber-700 dark:text-amber-300 mt-1">
                    {{
                      $t(
                        "Please add more resources to reach the minimum purchase amount."
                      )
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </UPricingPlan>

      <!-- Import BuyCreditsDrawer -->
      <BuyCreditsDrawer />
    </UPageSection>
  </UPage>
</template>
